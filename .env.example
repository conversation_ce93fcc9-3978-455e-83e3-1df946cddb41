# AI Interactive Book Chat - Environment Variables

# Google Gemini AI API Key (Required)
# Get your API key from: https://makersuite.google.com/app/apikey
API_KEY=your_gemini_api_key_here

# VAPI.ai Configuration (Optional - for voice features)
# Get your API key from: https://vapi.ai
VAPI_API_KEY=your_vapi_api_key_here
VAPI_ASSISTANT_ID=your_vapi_assistant_id_here

# Application Configuration (Optional)
APP_NAME="AI Interactive Book Chat"
APP_URL=https://ai-book-chat.app
APP_VERSION=1.0.0

# Custom API endpoint (if using a proxy)
# API_ENDPOINT=https://your-proxy-endpoint.com

# File Processing Settings
MAX_FILE_SIZE_MB=10
SUPPORTED_FORMATS=pdf,docx,txt

# Feature Flags
ENABLE_VOICE_FEATURES=true
ENABLE_PRESENTATIONS=true
ENABLE_MERMAID_DIAGRAMS=true

# Development Settings
DEBUG=false
NODE_ENV=development
VITE_DEV_PORT=5173

# Analytics (Optional)
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
MIXPANEL_TOKEN=your_mixpanel_token_here

# Error Tracking (Optional)
SENTRY_DSN=your_sentry_dsn_here

# Performance Settings
ENABLE_PERFORMANCE_MONITORING=false
CACHE_DURATION_MINUTES=30

# Instructions:
# 1. Copy this file to .env.local
# 2. Replace the placeholder values with your actual API keys
# 3. Remove any variables you don't need
# 4. Never commit .env.local to version control
# 5. Restart the development server after making changes
