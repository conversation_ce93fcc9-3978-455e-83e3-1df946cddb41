
import React, { useState, useCallback, useEffect } from 'react';
import { GoogleGenAI } from '@google/genai';
import { FileUpload } from './components/FileUpload';
import { BookViewer } from './components/BookViewer';
import { VoiceAgent } from './components/VoiceAgent';
import { TopicList } from './components/TopicList';
import { PresentationViewer } from './components/PresentationViewer';
import { LoadingSpinner } from './components/common/LoadingSpinner';
import { Button } from './components/common/Button';
import { parseFile } from './services/fileParserService';
import { extractTopics, generatePresentationOutline, generateImageDescriptionForTopic, generateMermaidSyntaxForTopic } from './services/geminiService';
import type { Topic, Presentation, Slide, InteractionType } from './types';
import { VapiButton } from './components/VapiButton'; // Placeholder for VAPI.ai functionality

// Ensure Mermaid is initialized after it's loaded
declare global {
  interface Window {
    mermaid?: any;
    pdfjsLib?: any;
    mammoth?: any;
    pdfjsWorkerSrc?: string;
  }
}

type MainView = 'welcome' | 'analysis_summary' | 'book_content' | 'presentation_content';

const App: React.FC = () => {
  const [apiKey, setApiKey] = useState<string | null>(null);
  const [aiInstance, setAiInstance] = useState<GoogleGenAI | null>(null);
  
  const [bookContent, setBookContent] = useState<string | null>(null);
  const [selectedText, setSelectedText] = useState<string>('');
  const [topics, setTopics] = useState<Topic[]>([]);
  const [currentPresentation, setCurrentPresentation] = useState<Presentation | null>(null);
  
  const [isProcessingFile, setIsProcessingFile] = useState<boolean>(false);
  const [isExtractingTopics, setIsExtractingTopics] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [currentMainView, setCurrentMainView] = useState<MainView>('welcome');

  const [isVapiActive, setIsVapiActive] = useState(false); // Placeholder state

  const globalIsLoading = isProcessingFile || isExtractingTopics;

  useEffect(() => {
    const envApiKey = process.env.API_KEY;
    if (envApiKey) {
      setApiKey(envApiKey);
      setAiInstance(new GoogleGenAI({ apiKey: envApiKey }));
    } else {
      console.warn("API_KEY not found in process.env. App functionality will be limited.");
      setError("API_KEY not found. Please ensure it is set in your environment.");
    }
    
    if (window.mermaid) {
      window.mermaid.initialize({ startOnLoad: false, theme: 'dark' });
    }
  }, []);

  const handleFileProcessed = useCallback(async (text: string) => {
    setBookContent(text);
    setTopics([]); // Clear previous topics
    setCurrentPresentation(null);
    setError(null);
    setSelectedText('');
    setCurrentMainView('analysis_summary'); // Switch to analysis summary view
    // setIsProcessingFile will be set to false by FileUpload component
  }, []);

  const handleAnalyzeBook = useCallback(async () => {
    if (!bookContent || !aiInstance) {
      setError("Book content or AI service not available for topic extraction.");
      return;
    }
    setIsExtractingTopics(true);
    setError(null);
    try {
      const extracted = await extractTopics(aiInstance, bookContent);
      setTopics(extracted);
    } catch (err) {
      console.error("Error extracting topics:", err);
      setError(`Failed to extract topics: ${err instanceof Error ? err.message : String(err)}`);
      setTopics([]); // Ensure topics are empty on error
    } finally {
      setIsExtractingTopics(false);
    }
  }, [bookContent, aiInstance]);

  // Automatically analyze book when view changes to analysis_summary and content is ready
  useEffect(() => {
    if (currentMainView === 'analysis_summary' && bookContent && topics.length === 0 && !isExtractingTopics && !error) {
      handleAnalyzeBook();
    }
  }, [currentMainView, bookContent, topics.length, isExtractingTopics, error, handleAnalyzeBook]);


  const handleGeneratePresentation = useCallback(async (topic: Topic) => {
    if (!bookContent || !aiInstance) {
      setError("Book content or AI service not available for presentation generation.");
      return;
    }
    setIsExtractingTopics(true); // Re-use for general AI loading for presentation
    setError(null);
    setCurrentPresentation(null);
    try {
      const presentationOutline = await generatePresentationOutline(aiInstance, topic.title, topic.summary + "\n\nRelevant context from book:\n" + bookContent.substring(0, 5000));
      
      const slidesWithVisuals: Slide[] = await Promise.all(
        presentationOutline.slides.map(async (slide, index): Promise<Slide> => {
          let imageUrl: string | undefined = undefined;
          let mermaidSyntax: string | undefined = undefined;
          let interaction: InteractionType | undefined = undefined;

          if (slide.imageSuggestion && index < 3) { 
            try {
              const imagePrompt = await generateImageDescriptionForTopic(aiInstance, slide.slideTitle + ": " + slide.contentPoints.join(' '));
              const placeholderImageUrl = `https://picsum.photos/seed/${encodeURIComponent(imagePrompt.substring(0,30))}/600/400`;
              imageUrl = placeholderImageUrl;
            } catch (e) { console.error("Error generating image description/placeholder:", e); }
          }
          
          if (slide.diagramSuggestion && index < 3) { 
             try {
                mermaidSyntax = await generateMermaidSyntaxForTopic(aiInstance, slide.diagramSuggestion.type, slide.diagramSuggestion.description);
             } catch (e) { console.error("Error generating mermaid syntax:", e); }
          }

          if (index === Math.floor(presentationOutline.slides.length / 2)) {
            interaction = { type: 'quiz', question: `What is the main point of "${slide.slideTitle}"?`, options: ['Option A', 'Option B', 'Option C'], correctAnswer: 'Option A' };
          } else if (index === presentationOutline.slides.length -1) {
             interaction = { type: 'video', videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4' };
          }

          return { ...slide, imageUrl, mermaidSyntax, interaction };
        })
      );

      setCurrentPresentation({ title: presentationOutline.title, slides: slidesWithVisuals });
      setCurrentMainView('presentation_content');
    } catch (err) {
      console.error("Error generating presentation:", err);
      setError(`Failed to generate presentation: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setIsExtractingTopics(false); // Re-use for general AI loading
    }
  }, [bookContent, aiInstance]);

  const handleTextSelect = (text: string) => {
    setSelectedText(text);
  };
  
  const toggleVapi = () => {
    if (!selectedText && !isVapiActive) {
        setError("Please select some text from the book to interact with.");
        return;
    }
    setError(null);
    setIsVapiActive(!isVapiActive);
    console.log(isVapiActive ? "Stopping VAPI" : "Starting VAPI with text:", selectedText);
  };


  if (!apiKey || !aiInstance) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-slate-900 p-4">
        <div className="bg-slate-800 p-8 rounded-lg shadow-2xl text-center">
          <h1 className="text-3xl font-bold text-sky-400 mb-4">AI Interactive Textbook</h1>
          <p className="text-red-400 text-xl">{error || "API Key not configured. Please set API_KEY environment variable."}</p>
          <p className="text-slate-400 mt-4">This application requires a valid Google Gemini API Key to function.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-900 to-sky-900 text-slate-100 p-4 md:p-6 lg:p-8">
      <header className="mb-6 text-center">
        <h1 className="text-4xl md:text-5xl font-extrabold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-sky-400 to-cyan-300">
          AI Interactive Textbook
        </h1>
      </header>

      {error && currentMainView !== 'analysis_summary' && ( // Show general errors, analysis_summary handles its own error display
        <div className="bg-red-700 border border-red-900 text-white px-4 py-3 rounded-md relative mb-4" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {globalIsLoading && <LoadingSpinner />}

      <div className="grid grid-cols-1 md:grid-cols-12 gap-6 flex-grow min-h-0">
        <aside className="md:col-span-4 bg-slate-800 p-6 rounded-xl shadow-2xl space-y-6 flex flex-col">
          <FileUpload onFileProcessed={handleFileProcessed} setIsLoading={setIsProcessingFile} setError={setError} />
          
          {topics.length > 0 && currentMainView !== 'presentation_content' && (
            <div className="flex-shrink overflow-y-auto">
                <TopicList topics={topics} onSelectTopic={handleGeneratePresentation} />
            </div>
          )}

          {selectedText && currentMainView === 'book_content' && (
             <div className="mt-auto pt-4 border-t border-slate-600 space-y-3"> 
                <h3 className="text-lg font-semibold text-sky-300 border-b border-sky-700 pb-2">Interact with Selected Text:</h3>
                <VoiceAgent selectedText={selectedText} aiInstance={aiInstance} />
                <VapiButton onClick={toggleVapi} isVapiActive={isVapiActive} />
             </div>
          )}
        </aside>

        <main className="md:col-span-8 bg-slate-800 p-6 rounded-xl shadow-2xl flex flex-col overflow-hidden">
          {currentMainView === 'welcome' && (
            <div className="flex flex-col items-center justify-center h-full text-slate-400">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-24 h-24 mb-4 opacity-50">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6-2.292m0 0V21M12 6.042A8.967 8.967 0 0 1 18 3.75m-6 2.292V3.75m0 2.292A8.966 8.966 0 0 0 6 3.75m6 2.292V21m0 0V3.75M3 12h18M3 12a8.967 8.967 0 0 1 0-4.5m18 4.5a8.967 8.967 0 0 0 0-4.5m0 4.5V7.5m0 4.5v4.5m0-4.5H3m0 0V7.5m0 4.5v4.5" />
              </svg>
              <p className="text-xl">Upload a book to get started.</p>
              <p className="text-sm mt-2">Supported formats: TXT, PDF, DOCX</p>
            </div>
          )}

          {currentMainView === 'analysis_summary' && bookContent && (
            <div className="flex flex-col h-full">
              <h2 className="text-2xl font-semibold text-sky-300 border-b border-sky-700 pb-2 mb-4">Book Analysis</h2>
              {isExtractingTopics && (
                <div className="flex flex-col items-center justify-center flex-grow">
                  <p className="text-xl text-slate-300 mb-3">Analyzing book and extracting topics...</p>
                  {/* Spinner is global, but a local one could be added if preferred */}
                </div>
              )}
              {!isExtractingTopics && error && topics.length === 0 && (
                <div className="flex flex-col items-center justify-center flex-grow">
                  <p className="text-red-400 text-lg">Error during analysis: {error}</p>
                  <Button onClick={() => setCurrentMainView('book_content')} className="mt-4 bg-sky-600 hover:bg-sky-500">
                    View Book Content Anyway
                  </Button>
                </div>
              )}
              {!isExtractingTopics && !error && topics.length === 0 && (
                 <div className="flex flex-col items-center justify-center flex-grow">
                    <p className="text-slate-400 text-lg">No topics were extracted from the book, or analysis was incomplete.</p>
                    <Button onClick={() => setCurrentMainView('book_content')} className="mt-4 bg-sky-600 hover:bg-sky-500">
                         View Book Content
                    </Button>
                 </div>
              )}
              {!isExtractingTopics && topics.length > 0 && (
                <div className="flex-grow overflow-y-auto">
                  <h3 className="text-xl text-cyan-300 mb-3">Extracted Topics:</h3>
                  <TopicList topics={topics} onSelectTopic={handleGeneratePresentation} />
                  <Button onClick={() => setCurrentMainView('book_content')} className="mt-6 bg-sky-600 hover:bg-sky-500">
                    View Full Book Content
                  </Button>
                </div>
              )}
               {!isExtractingTopics && !bookContent && ( // Should not happen if logic is correct
                    <div className="flex flex-col items-center justify-center h-full text-slate-400">
                        <p className="text-xl">Error: Book content is missing for analysis.</p>
                         <Button onClick={() => setCurrentMainView('welcome')} className="mt-4 bg-sky-600 hover:bg-sky-500">
                            Back to Upload
                        </Button>
                    </div>
                )}
            </div>
          )}

          {currentMainView === 'book_content' && bookContent && (
            <>
              <BookViewer content={bookContent} onTextSelect={handleTextSelect} />
              <div className="mt-4 flex-shrink-0">
                 <Button onClick={() => setCurrentMainView('analysis_summary')} className="bg-cyan-600 hover:bg-cyan-500">
                    Back to Topic Summary
                 </Button>
              </div>
            </>
          )}

          {currentMainView === 'presentation_content' && currentPresentation && (
            <>
              <Button 
                onClick={() => topics.length > 0 ? setCurrentMainView('analysis_summary') : setCurrentMainView('book_content')} 
                className="mb-4 bg-cyan-600 hover:bg-cyan-500 flex-shrink-0"
              >
                {topics.length > 0 ? "Back to Topic Summary" : "Back to Book Content"}
              </Button>
              <div className="flex-grow overflow-y-auto">
                <PresentationViewer presentation={currentPresentation} />
              </div>
            </>
          )}
        </main>
      </div>
    </div>
  );
};

export default App;
    