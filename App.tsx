
import React, { useState, useCallback, useEffect } from 'react';
import { GoogleGenAI } from '@google/genai';
import { FileUpload } from './components/FileUpload';
import { BookViewer } from './components/BookViewer';
import { VoiceAgent } from './components/VoiceAgent';
import { TopicList } from './components/TopicList';
import { PresentationViewer } from './components/PresentationViewer';
import { LoadingSpinner } from './components/common/LoadingSpinner';
import { Button } from './components/common/Button';
import { parseFile } from './services/fileParserService';
import { extractTopics, generatePresentationOutline, generatePresentationWithWebContent, generateImageDescriptionForTopic, generateMermaidSyntaxForTopic } from './services/geminiService';
import type { Topic, Presentation, Slide, InteractionType } from './types';
import { VapiButton } from './components/VapiButton'; // Placeholder for VAPI.ai functionality

// Ensure Mermaid is initialized after it's loaded
declare global {
  interface Window {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mermaid?: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    pdfjsLib?: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mammoth?: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    pdfjsWorkerSrc?: string;
  }
}


const App: React.FC = () => {
  const [apiKey, setApiKey] = useState<string | null>(null);
  const [aiInstance, setAiInstance] = useState<GoogleGenAI | null>(null);

  const [bookContent, setBookContent] = useState<string | null>(null);
  const [selectedText, setSelectedText] = useState<string>('');
  const [topics, setTopics] = useState<Topic[]>([]);
  const [currentPresentation, setCurrentPresentation] = useState<Presentation | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [currentView, setCurrentView] = useState<'book' | 'presentation'>('book');

  const [isVapiActive, setIsVapiActive] = useState(false); // Placeholder state

  useEffect(() => {
    // Attempt to get API_KEY from environment.
    // In a real browser environment, this needs to be handled via build-time replacement or a backend.
    // For this exercise, we simulate it being available.
    const envApiKey = process.env.API_KEY;
    if (envApiKey) {
      setApiKey(envApiKey);
      setAiInstance(new GoogleGenAI({ apiKey: envApiKey }));
    } else {
      console.warn("API_KEY not found in process.env. App functionality will be limited.");
      setError("API_KEY not found. Please ensure it is set in your environment.");
    }

    if (window.mermaid) {
      window.mermaid.initialize({ startOnLoad: false, theme: 'dark' });
    }
  }, []);

  const handleFileProcessed = useCallback(async (text: string) => {
    setBookContent(text);
    setTopics([]);
    setCurrentPresentation(null);
    setError(null);
    setCurrentView('book');
    setSelectedText(''); // Clear selected text when new file is processed
  }, []);

  const handleAnalyzeBook = useCallback(async () => {
    if (!bookContent || !aiInstance) {
      setError("Book content or AI service not available.");
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const extracted = await extractTopics(aiInstance, bookContent);
      setTopics(extracted);
    } catch (err) {
      console.error("Error extracting topics:", err);
      setError(`Failed to extract topics: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setIsLoading(false);
    }
  }, [bookContent, aiInstance]);

  const handleGeneratePresentation = useCallback(async (topic: Topic) => {
    if (!bookContent || !aiInstance) {
      setError("Book content or AI service not available for presentation generation.");
      return;
    }
    setIsLoading(true);
    setError(null);
    setCurrentPresentation(null);
    try {
      // Use the enhanced presentation generation with real content
      const presentationOutline = await generatePresentationWithWebContent(
        aiInstance,
        topic.title,
        topic.summary + "\n\nRelevant context from book:\n" + bookContent.substring(0, 5000)
      );

      // The enhanced presentation already includes real content, images, and interactions
      // We just need to add Mermaid diagrams for slides that need them
      const slidesWithVisuals: Slide[] = await Promise.all(
        presentationOutline.slides.map(async (slide, index): Promise<Slide> => {
          let mermaidSyntax: string | undefined = undefined;

          // Generate Mermaid diagrams for slides that have diagram suggestions
          if (slide.diagramSuggestion && index < 3) {
            try {
              mermaidSyntax = await generateMermaidSyntaxForTopic(
                aiInstance,
                slide.diagramSuggestion.type,
                slide.diagramSuggestion.description
              );
            } catch (e) {
              console.error("Error generating mermaid syntax:", e);
            }
          }

          // Add quiz interaction for middle slides if not already present
          if (!slide.interaction && index === Math.floor(presentationOutline.slides.length / 2)) {
            const isArabic = slide.slideTitle.match(/[\u0600-\u06FF]/);
            slide.interaction = {
              type: 'quiz',
              question: isArabic ?
                `ما هي النقطة الرئيسية في "${slide.slideTitle}"؟` :
                `What is the main point of "${slide.slideTitle}"?`,
              options: isArabic ?
                ['الخيار الأول', 'الخيار الثاني', 'الخيار الثالث'] :
                ['Option A', 'Option B', 'Option C'],
              correctAnswer: isArabic ? 'الخيار الأول' : 'Option A'
            };
          }

          return { ...slide, mermaidSyntax };
        })
      );

      setCurrentPresentation({ title: presentationOutline.title, slides: slidesWithVisuals });
      setCurrentView('presentation');
    } catch (err) {
      console.error("Error generating presentation:", err);
      setError(`Failed to generate presentation: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setIsLoading(false);
    }
  }, [bookContent, aiInstance]);

  const handleTextSelect = (text: string) => {
    setSelectedText(text);
  };

  // Placeholder for VAPI.ai integration
  const toggleVapi = () => {
    if (!selectedText && !isVapiActive) {
        setError("Please select some text from the book to interact with.");
        return;
    }
    setError(null);
    setIsVapiActive(!isVapiActive);
    // Here you would initialize or terminate VAPI.ai with the selectedText
    console.log(isVapiActive ? "Stopping VAPI" : "Starting VAPI with text:", selectedText);
  };


  if (!apiKey || !aiInstance) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-slate-900 p-4">
        <div className="bg-slate-800 p-8 rounded-lg shadow-2xl text-center">
          <h1 className="text-3xl font-bold text-sky-400 mb-4">AI Interactive Textbook</h1>
          <p className="text-red-400 text-xl">{error || "API Key not configured. Please set API_KEY environment variable."}</p>
          <p className="text-slate-400 mt-4">This application requires a valid Google Gemini API Key to function.</p>
        </div>
      </div>
    );
  }


  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-900 to-sky-900 text-slate-100 p-4 md:p-6 lg:p-8">
      <header className="mb-6 text-center">
        <h1 className="text-4xl md:text-5xl font-extrabold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-sky-400 to-cyan-300">
          AI Interactive Textbook
        </h1>
      </header>

      {error && (
        <div className="bg-red-700 border border-red-900 text-white px-4 py-3 rounded-md relative mb-4" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {isLoading && <LoadingSpinner />}

      <div className="grid grid-cols-1 md:grid-cols-12 gap-6 flex-grow min-h-0"> {/* Added min-h-0 for flex children */}
        {/* Left Panel / Main Upload Area */}
        <aside className="md:col-span-4 bg-slate-800 p-6 rounded-xl shadow-2xl space-y-6 flex flex-col"> {/* Ensure aside can shrink/grow */}
          <FileUpload onFileProcessed={handleFileProcessed} setIsLoading={setIsLoading} setError={setError} />

          {bookContent && currentView === 'book' && (
            <Button onClick={handleAnalyzeBook} disabled={isLoading || !bookContent} className="w-full bg-sky-600 hover:bg-sky-500">
              Analyze Book & Extract Topics
            </Button>
          )}

          {topics.length > 0 && currentView === 'book' && (
            <div className="flex-shrink overflow-y-auto"> {/* Allow topic list to scroll if needed */}
                <TopicList topics={topics} onSelectTopic={handleGeneratePresentation} />
            </div>
          )}

          {selectedText && currentView === 'book' && (
             <div className="mt-auto pt-4 border-t border-slate-600 space-y-3"> {/* Pushes to bottom if space allows */}
                <h3 className="text-lg font-semibold text-sky-300 border-b border-sky-700 pb-2">Interact with Selected Text:</h3>
                <VoiceAgent selectedText={selectedText} aiInstance={aiInstance} />
                <VapiButton onClick={toggleVapi} isVapiActive={isVapiActive} />
             </div>
          )}
        </aside>

        {/* Right Panel / Content Area */}
        <main className="md:col-span-8 bg-slate-800 p-6 rounded-xl shadow-2xl flex flex-col overflow-hidden"> {/* Flex col and overflow hidden for BookViewer */}
          {currentView === 'book' && bookContent && (
            <BookViewer content={bookContent} onTextSelect={handleTextSelect} />
          )}
          {currentView === 'presentation' && currentPresentation && (
            <>
              <Button onClick={() => setCurrentView('book')} className="mb-4 bg-cyan-600 hover:bg-cyan-500 flex-shrink-0">
                Back to Book
              </Button>
              <div className="flex-grow overflow-y-auto">
                <PresentationViewer presentation={currentPresentation} />
              </div>
            </>
          )}
          {!bookContent && !currentPresentation && currentView === 'book' && (
            <div className="flex flex-col items-center justify-center h-full text-slate-400">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-24 h-24 mb-4 opacity-50">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6-2.292m0 0V21M12 6.042A8.967 8.967 0 0 1 18 3.75m-6 2.292V3.75m0 2.292A8.966 8.966 0 0 0 6 3.75m6 2.292V21m0 0V3.75M3 12h18M3 12a8.967 8.967 0 0 1 0-4.5m18 4.5a8.967 8.967 0 0 0 0-4.5m0 4.5V7.5m0 4.5v4.5m0-4.5H3m0 0V7.5m0 4.5v4.5" />
              </svg>
              <p className="text-xl">Upload a book to get started.</p>
              <p className="text-sm mt-2">Supported formats: TXT, PDF, DOCX</p>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default App;
