# Contributing to AI Interactive Book Chat

Thank you for your interest in contributing to AI Interactive Book Chat! We welcome contributions from everyone.

## 🚀 Getting Started

### Prerequisites

- Node.js (v18.0.0 or higher)
- npm (v8.0.0 or higher)
- Git
- A Google Gemini API key

### Development Setup

1. **Fork the repository**
   ```bash
   git clone https://github.com/your-username/ai-interactive-book-chat.git
   cd ai-interactive-book-chat
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your API keys
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

## 📋 How to Contribute

### Reporting Bugs

1. Check if the bug has already been reported in [Issues](https://github.com/ai-book-chat/ai-interactive-book-chat/issues)
2. If not, create a new issue with:
   - Clear description of the bug
   - Steps to reproduce
   - Expected vs actual behavior
   - Screenshots if applicable
   - Environment details (OS, browser, Node.js version)

### Suggesting Features

1. Check [existing feature requests](https://github.com/ai-book-chat/ai-interactive-book-chat/issues?q=is%3Aissue+is%3Aopen+label%3Aenhancement)
2. Create a new issue with:
   - Clear description of the feature
   - Use cases and benefits
   - Possible implementation approach

### Code Contributions

1. **Create a branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow the existing code style
   - Add comments for complex logic
   - Update documentation if needed

3. **Test your changes**
   ```bash
   npm run type-check
   npm run build
   ```

4. **Commit your changes**
   ```bash
   git commit -m "feat: add your feature description"
   ```

5. **Push and create a Pull Request**
   ```bash
   git push origin feature/your-feature-name
   ```

## 📝 Code Style Guidelines

### TypeScript
- Use TypeScript for all new code
- Define proper types and interfaces
- Avoid `any` type when possible

### React Components
- Use functional components with hooks
- Follow the existing component structure
- Use proper prop types

### Styling
- Use Tailwind CSS classes
- Follow the existing design system
- Ensure responsive design

### File Organization
```
components/
├── common/          # Reusable UI components
├── ComponentName.tsx
└── ...
services/            # API and utility services
types.ts             # Type definitions
```

## 🧪 Testing

Currently, the project doesn't have automated tests, but we welcome contributions to add:
- Unit tests for components
- Integration tests for services
- End-to-end tests for user flows

## 📚 Documentation

When contributing, please:
- Update README.md if needed
- Add JSDoc comments to functions
- Update CHANGELOG.md for significant changes

## 🎯 Priority Areas

We especially welcome contributions in:
- Testing infrastructure
- Accessibility improvements
- Performance optimizations
- Mobile experience enhancements
- Voice interaction features
- Error handling improvements

## 💬 Community

- Join our [Discussions](https://github.com/ai-book-chat/ai-interactive-book-chat/discussions)
- Follow us on [Twitter](https://twitter.com/aibookchat)
- Email us at [<EMAIL>](mailto:<EMAIL>)

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

Thank you for contributing! 🎉
