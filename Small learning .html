<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Robotics Project: Small Learning Robots</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ startOnLoad: true });
    </script>
    <style>
        /* Base font family for the entire body */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f4f8; /* Light gray background for the page */
            color: #333; /* Default text color */
        }
        /* Container for content, centered with max-width */
        .container {
            max-width: 1200px;
        }
        /* Primary button styling */
        .btn-primary {
            background-color: #4f46e5; /* Indigo-600 */
            color: white;
            padding: 0.75rem 1.5rem; /* Vertical and horizontal padding */
            border-radius: 0.5rem; /* Rounded corners */
            font-weight: 600; /* Semi-bold text */
            transition: background-color 0.3s ease; /* Smooth transition for hover effect */
        }
        /* Hover effect for primary button */
        .btn-primary:hover {
            background-color: #4338ca; /* Darker Indigo-700 on hover */
        }
        /* Styling for feature cards */
        .card {
            background-color: white;
            border-radius: 0.75rem; /* Rounded corners */
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow */
            padding: 1.5rem; /* Padding inside the card */
            transition: transform 0.3s ease; /* Smooth transition for hover effect */
        }
        /* Hover effect for feature cards */
        .card:hover {
            transform: translateY(-5px); /* Lift card slightly on hover */
        }
        /* Gradient background for the hero section */
        .hero-section {
            background: linear-gradient(to right, #6366f1, #818cf8); /* Indigo gradient */
        }
        /* Styling for the presentation result area */
        #presentationResult {
            background-color: #f8f9fa;
            padding: 20px;
            margin-top: 20px;
            text-align: right;
            direction: rtl; /* Right-to-left text direction for Arabic */
            border: 1px solid #ccc;
            border-radius: 10px;
            font-size: 18px;
            line-height: 1.6;
        }
        /* Styling for headings and lists within the presentation result */
        #presentationResult h3, #presentationResult h2, #presentationResult ul {
            color: #0b5ed7; /* Blue color for headings and lists */
        }
        #presentationResult ul {
            list-style: disc; /* Disc style for list items */
            padding-right: 20px; /* Padding for right alignment in RTL */
        }
    </style>
</head>
<body class="antialiased">
    <div class="min-h-screen flex flex-col items-center justify-center">

        <section class="hero-section w-full py-20 text-white text-center rounded-b-xl shadow-lg">
            <div class="container mx-auto px-4">
                <h1 class="text-4xl sm:text-5xl md:text-6xl font-extrabold leading-tight mb-4 rounded-lg">
                    Empowering Minds with <br class="hidden sm:inline">Small Learning Robots
                </h1>
                <p class="text-lg sm:text-xl md:text-2xl mb-8 opacity-90 rounded-lg">
                    Discover the future of education through interactive, intelligent robotics.
                </p>
                <a href="#contact" class="btn-primary inline-block text-lg shadow-lg hover:shadow-xl rounded-full">
                    Learn More & Get Started
                </a>
            </div>
        </section>

        <section id="features" class="w-full py-16 bg-white rounded-xl shadow-md mt-8">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl sm:text-4xl font-bold text-center mb-12 text-gray-800">Key Features</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="card text-center">
                        <div class="text-5xl mb-4 text-indigo-500">🤖</div> <h3 class="text-xl font-semibold mb-2 text-gray-700">Interactive Learning</h3>
                        <p class="text-gray-600">
                            Our robots engage users with hands-on activities and real-time feedback, making learning fun and effective.
                        </p>
                    </div>
                    <div class="card text-center">
                        <div class="text-5xl mb-4 text-green-500">🧠</div> <h3 class="text-xl font-semibold mb-2 text-gray-700">AI-Powered Adaptation</h3>
                        <p class="text-gray-600">
                            Equipped with advanced AI, the robots adapt to individual learning paces and styles, providing personalized experiences.
                        </p>
                    </div>
                    <div class="card text-center">
                        <div class="text-5xl mb-4 text-yellow-500">💡</div> <h3 class="text-xl font-semibold mb-2 text-gray-700">STEM Skill Development</h3>
                        <p class="text-gray-600">
                            Foster critical thinking, problem-solving, and coding skills essential for future innovators.
                        </p>
                    </div>
                    <div class="card text-center">
                        <div class="text-5xl mb-4 text-red-500">🌐</div> <h3 class="text-xl font-semibold mb-2 text-gray-700">Community & Collaboration</h3>
                        <p class="text-gray-600">
                            Connect with a vibrant community of learners and educators, sharing projects and ideas.
                        </p>
                    </div>
                    <div class="card text-center">
                        <div class="text-5xl mb-4 text-purple-500">🛠️</div> <h3 class="text-xl font-semibold mb-2 text-gray-700">Easy to Use & Program</h3>
                        <p class="text-gray-600">
                            Designed for all ages, our robots are intuitive to operate and easy to program with visual interfaces.
                        </p>
                    </div>
                    <div class="card text-center">
                        <div class="text-5xl mb-4 text-blue-500">🚀</div> <h3 class="text-xl font-semibold mb-2 text-gray-700">Future-Proof Technology</h3>
                        <p class="text-gray-600">
                            Built with modularity in mind, our robots are ready for future upgrades and expanded functionalities.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <section id="contact" class="w-full py-16 bg-indigo-500 text-white text-center rounded-t-xl shadow-lg mt-8">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl sm:text-4xl font-bold mb-6">Ready to Transform Learning?</h2>
                <p class="text-lg sm:text-xl mb-8 opacity-90">
                    Join us in shaping the next generation of innovators.
                </p>
                <a href="#" class="btn-primary inline-block text-lg shadow-lg hover:shadow-xl rounded-full">
                    Contact Us Today!
                </a>
            </div>
        </section>

        <section id="presentation-section" class="w-full py-16 bg-white rounded-xl shadow-md mt-8" style="display:block;">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl sm:text-4xl font-bold text-center mb-12 text-gray-800">📊 إنشاء عرض تقديمي للموضوع</h2>
                <select id="topicSelect" class="block w-full p-2 border border-gray-300 rounded-md mb-4"></select>
                <div class="flex flex-wrap justify-center gap-4">
                    <button onclick="generatePresentation()" class="btn-primary">أنشئ العرض</button>
                    <button onclick="savePresentationToLocal()" class="btn-primary">💾 حفظ محلياً</button>
                    <button onclick="downloadPresentationAsPDF()" class="btn-primary">📄 تحميل PDF</button>
                    <button onclick="loadSavedPresentation()" class="btn-primary">📂 استرجاع عرض محفوظ</button>
                </div>
                <div id="presentationResult" class="mt-8 p-4 border rounded-lg shadow-inner text-right" dir="rtl">
                    <p class="text-gray-600">اختر موضوعًا واضغط "أنشئ العرض" لتوليد عرض تقديمي.</p>
                </div>
            </div>
        </section>

        <footer class="w-full py-8 text-center text-gray-600 text-sm mt-8">
            <p>&copy; 2025 Robotics Project. All rights reserved.</p>
        </footer>

    </div>

    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/showdown/dist/showdown.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.3/html2pdf.bundle.min.js"></script>

    <script>
        // Placeholder for topic data (in a real app, this would come from a backend after file upload)
        let availableTopics = [
            { title: "الذكاء الاصطناعي", summary: "شرح مفصل حول الذكاء الاصطناعي" },
            { title: "الشبكات العصبية", summary: "تعريف وأساليب الشبكات العصبية" },
            { title: "تحليل البيانات", summary: "كيفية تحليل البيانات في العصر الرقمي" },
            { title: "تعلم الآلة", summary: "المبادئ الأساسية لتعلم الآلة" },
            { title: "تدفق العمليات", summary: "مراحل تدفق العمليات في الأنظمة" }
        ];

        document.addEventListener('DOMContentLoaded', () => {
            // Populate the topic selection dropdown on page load
            const select = document.getElementById("topicSelect");
            availableTopics.forEach(topic => {
                const option = document.createElement("option");
                option.value = topic.summary; // Using summary as value for simplicity
                option.text = topic.title;
                select.appendChild(option);
            });
        });

        /**
         * Generates a presentation based on the selected topic.
         * In a real application, this would fetch content from a backend API.
         */
        async function generatePresentation() {
            const topicSelect = document.getElementById("topicSelect");
            const selectedTopicSummary = topicSelect.value;
            const selectedTopicTitle = topicSelect.options[topicSelect.selectedIndex].text;

            // Simulate fetching presentation content (replace with actual API call)
            // For now, we construct a Markdown string
            const markdown = `
# ${selectedTopicTitle}
---

## المقدمة
شرح عام عن "${selectedTopicTitle}"

## النقاط الرئيسية
- الفكرة الأولى المتعلقة بـ ${selectedTopicTitle}.
- الفكرة الثانية وتطبيقاتها.
- تطبيقات عملية ومستقبلية.

## رسم توضيحي
\`\`\`mermaid
graph TD;
  A[بداية] --> B[${selectedTopicTitle}];
  B --> C[تطبيقات];
  C --> D[الخلاصة];
\`\`\`

## الخلاصة
تلخيص شامل للموضوع ونتائج.

---

📌 تم إنشاؤه تلقائيًا باستخدام مساعد AI
            `;

            const converter = new showdown.Converter();
            const html = converter.makeHtml(markdown);
            document.getElementById("presentationResult").innerHTML = html;

            // Generate an automatic diagram based on keywords in the topic
            generateAutoDiagramFromTopic(selectedTopicTitle);

            // Re-run Mermaid to render any new diagrams
            mermaid.run();
        }

        /**
         * Generates an automatic Mermaid diagram based on keywords in the topic.
         * Appends the diagram to the presentation result.
         * @param {string} topic - The topic title.
         */
        function generateAutoDiagramFromTopic(topic) {
            const lower = topic.toLowerCase();
            let diagram = "";

            if (lower.includes("شبكة") || lower.includes("networks")) {
                diagram = `
graph TD
  A[العميل] -->|طلب| B[الخادم]
  B -->|رد| A
  B --> C[قاعدة البيانات]
                `;
            } else if (lower.includes("نموذج") || lower.includes("model") || lower.includes("ذكاء") || lower.includes("ai")) {
                diagram = `
graph LR
  Input --> Preprocessing --> Model --> Prediction
                `;
            } else if (lower.includes("تدفق") || lower.includes("flow")) {
                diagram = `
flowchart TD
  Start --> Step1 --> Step2 --> End
                `;
            }

            if (diagram) {
                const diagramContainer = document.createElement("div");
                diagramContainer.className = "mermaid mt-4"; // Add margin top for spacing
                diagramContainer.innerText = diagram;
                document.getElementById("presentationResult").appendChild(diagramContainer);
                // Mermaid.run() will be called by generatePresentation() after this
            }
        }

        /**
         * Saves the current presentation content to local storage.
         */
        function savePresentationToLocal() {
            const content = document.getElementById("presentationResult").innerHTML;
            localStorage.setItem("lastPresentation", content);
            // Using a simple alert for demonstration; consider a custom modal in production
            alert("✅ تم حفظ العرض التقديمي محلياً.");
        }

        /**
         * Downloads the current presentation content as a PDF.
         * Uses html2pdf.js library.
         */
        function downloadPresentationAsPDF() {
            const element = document.getElementById("presentationResult");
            const opt = {
                margin:       0.5,
                filename:     'presentation.pdf',
                image:        { type: 'jpeg', quality: 0.98 },
                html2canvas:  { scale: 2 },
                jsPDF:        { unit: 'in', format: 'letter', orientation: 'portrait' }
            };

            html2pdf().from(element).set(opt).save();
        }

        /**
         * Loads the last saved presentation from local storage.
         */
        function loadSavedPresentation() {
            const saved = localStorage.getItem("lastPresentation");
            if (saved) {
                document.getElementById("presentationResult").innerHTML = saved;
                mermaid.run(); // Re-run Mermaid to render diagrams from loaded content
                alert("✅ تم تحميل العرض المحفوظ من المتصفح.");
            } else {
                alert("❌ لا يوجد عرض محفوظ.");
            }
        }

        // Placeholder for file upload and TTS functions (would require backend)
        // async function handleUpload() { /* ... */ }
        // async function playVoice() { /* ... */ }
        // async function generateViaGenspark() { /* ... */ }
    </script>
</body>
</html>
