import React, { useCallback, useState, useEffect, useMemo } from 'react';
import { CHARACTERS_PER_PAGE } from '../constants';
import { Button } from './common/Button';
import { QuickTTS } from './QuickTTS';

interface BookViewerProps {
  content: string;
  onTextSelect: (selectedText: string) => void;
}

export const BookViewer: React.FC<BookViewerProps> = ({ content, onTextSelect }) => {
  const [currentPage, setCurrentPage] = useState(0); // Master current page index
  const [displayPage, setDisplayPage] = useState(0); // Index of content currently in DOM
  const [pagedContent, setPagedContent] = useState<string[]>([]);
  const [totalPages, setTotalPages] = useState(0);
  const [animationClass, setAnimationClass] = useState('page-enter');

  useEffect(() => {
    if (content) {
      const pages: string[] = [];
      let remainingContent = content;
      while (remainingContent.length > 0) {
        let pageText = remainingContent.substring(0, CHARACTERS_PER_PAGE);
        const lastParagraph = pageText.lastIndexOf('\n\n');
        const lastSentence = pageText.lastIndexOf('. ');

        if (remainingContent.length > CHARACTERS_PER_PAGE) {
            if (lastParagraph > CHARACTERS_PER_PAGE / 2) {
                pageText = remainingContent.substring(0, lastParagraph + 2);
            } else if (lastSentence > CHARACTERS_PER_PAGE / 2) {
                pageText = remainingContent.substring(0, lastSentence + 1);
            }
        }

        pages.push(pageText);
        remainingContent = remainingContent.substring(pageText.length);
      }
      setPagedContent(pages);
      setTotalPages(pages.length);
      setCurrentPage(0);
      setDisplayPage(0);
      setAnimationClass('page-enter'); // Initial state for new content
    } else {
      setPagedContent([]);
      setTotalPages(0);
      setCurrentPage(0);
      setDisplayPage(0);
      setAnimationClass('page-enter');
    }
  }, [content]);

  const handleMouseUp = useCallback(() => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim().length > 0) {
      onTextSelect(selection.toString().trim());
    }
  }, [onTextSelect]);

  const changePage = (newPageIndex: number, direction: 'next' | 'prev') => {
    if (direction === 'next') {
      setAnimationClass('page-exit-left');
    } else {
      setAnimationClass('page-exit-right');
    }

    setTimeout(() => {
      setDisplayPage(newPageIndex);
      setCurrentPage(newPageIndex);
      if (direction === 'next') {
        setAnimationClass('page-enter-from-right');
      } else {
        setAnimationClass('page-enter-from-left');
      }

      // Force reflow to apply start animation class, then apply final class for transition
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
           setAnimationClass('page-enter');
        });
      });
    }, 600); // Should match CSS transition duration
  };

  const goToNextPage = () => {
    if (currentPage < totalPages - 1) {
      changePage(currentPage + 1, 'next');
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 0) {
      changePage(currentPage - 1, 'prev');
    }
  };

  const currentVisibleContent = useMemo(() => {
    return pagedContent[displayPage] || "No content loaded or an error occurred.";
  }, [pagedContent, displayPage]);


  if (!content && totalPages === 0) { // Check totalPages too to avoid flicker during initial load
    return (
         <div className="flex flex-col items-center justify-center h-full text-slate-400">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-24 h-24 mb-4 opacity-50">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6-2.292m0 0V21M12 6.042A8.967 8.967 0 0 1 18 3.75m-6 2.292V3.75m0 2.292A8.966 8.966 0 0 0 6 3.75m6 2.292V21m0 0V3.75M3 12h18M3 12a8.967 8.967 0 0 1 0-4.5m18 4.5a8.967 8.967 0 0 0 0-4.5m0 4.5V7.5m0 4.5v4.5m0-4.5H3m0 0V7.5m0 4.5v4.5" />
            </svg>
            <p className="text-xl">Content will appear here once a book is processed.</p>
        </div>
    );
  }

  return (
    <div className="space-y-4 flex flex-col h-full">
      <div className="flex items-center justify-between border-b border-sky-700 pb-2">
        <h2 className="text-2xl font-semibold text-sky-300">Book Content</h2>
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <QuickTTS
            text={currentVisibleContent}
            size="sm"
            className="opacity-80 hover:opacity-100"
          />
          <span className="text-xs text-slate-400">
            {totalPages > 0 ? `Page ${currentPage + 1}/${totalPages}` : ''}
          </span>
        </div>
      </div>

      <div
        className="flex-grow overflow-hidden relative"
        style={{ perspective: '1500px' }} /* Perspective for 3D effect */
      >
        <div
          onMouseUp={handleMouseUp}
          className={`prose prose-invert max-w-none bg-slate-700 p-6 rounded-lg shadow text-slate-200 whitespace-pre-wrap overflow-y-auto h-full selection:bg-sky-500 selection:text-white page-content ${animationClass}`}
          style={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word' }}
        >
          {currentVisibleContent}
        </div>
      </div>

      {totalPages > 0 && (
        <div className="flex justify-between items-center pt-4 border-t border-slate-600 flex-shrink-0">
          <Button onClick={goToPrevPage} disabled={currentPage === 0} className="bg-sky-600 hover:bg-sky-500">
            Previous Page
          </Button>
          <p className="text-sm text-slate-400">
            Page {currentPage + 1} of {totalPages}
          </p>
          <Button onClick={goToNextPage} disabled={currentPage >= totalPages - 1} className="bg-sky-600 hover:bg-sky-500">
            Next Page
          </Button>
        </div>
      )}
      <p className="text-sm text-slate-400 italic mt-2 text-center flex-shrink-0">Select text on the current page to interact with it.</p>
    </div>
  );
};