// مكون معاينة المحتوى المحسن - Enhanced Content Preview Component
// يعرض معاينة للمحتوى الحقيقي والصور والفيديوهات قبل إنشاء العرض التقديمي

import React, { useState, useEffect } from 'react';
import type { GoogleGenAI } from '@google/genai';
import { searchAllContent, type SearchResult, type ImageResult, type VideoResult } from '../services/webSearchService';
import { Button } from './common/Button';

// Helper function to detect Arabic text
const isArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
  const totalChars = text.replace(/\s/g, '').length;
  return arabicChars ? (arabicChars.length / totalChars) > 0.3 : false;
};

interface EnhancedContentPreviewProps {
  topic: string;
  context: string;
  aiInstance: GoogleGenAI;
  onCreatePresentation: () => void;
  onCancel: () => void;
}

interface ContentData {
  information: SearchResult[];
  images: ImageResult[];
  videos: VideoResult[];
  statistics: string[];
}

export const EnhancedContentPreview: React.FC<EnhancedContentPreviewProps> = ({
  topic,
  context,
  aiInstance,
  onCreatePresentation,
  onCancel
}) => {
  const [contentData, setContentData] = useState<ContentData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImages, setSelectedImages] = useState<Set<number>>(new Set());
  const [selectedVideos, setSelectedVideos] = useState<Set<number>>(new Set());

  const isArabic = isArabicText(topic + context);

  useEffect(() => {
    const fetchContent = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const content = await searchAllContent(aiInstance, topic, '', context);
        setContentData(content);
        
        // Pre-select first few items
        setSelectedImages(new Set([0, 1]));
        setSelectedVideos(new Set([0]));
      } catch (err) {
        console.error('Error fetching enhanced content:', err);
        setError(isArabic ? 
          'حدث خطأ في جلب المحتوى المحسن. سيتم إنشاء العرض التقديمي بالمحتوى الأساسي.' :
          'Error fetching enhanced content. Presentation will be created with basic content.'
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchContent();
  }, [topic, context, aiInstance, isArabic]);

  const toggleImageSelection = (index: number) => {
    const newSelection = new Set(selectedImages);
    if (newSelection.has(index)) {
      newSelection.delete(index);
    } else {
      newSelection.add(index);
    }
    setSelectedImages(newSelection);
  };

  const toggleVideoSelection = (index: number) => {
    const newSelection = new Set(selectedVideos);
    if (newSelection.has(index)) {
      newSelection.delete(index);
    } else {
      newSelection.add(index);
    }
    setSelectedVideos(newSelection);
  };

  if (isLoading) {
    return (
      <div className={`bg-slate-800 p-6 rounded-xl shadow-2xl ${isArabic ? 'font-arabic' : ''}`} dir={isArabic ? 'rtl' : 'ltr'}>
        <div className="flex items-center justify-center space-x-3">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sky-400"></div>
          <p className="text-slate-300">
            {isArabic ? 'جاري البحث عن محتوى حقيقي ومحدث...' : 'Searching for real and updated content...'}
          </p>
        </div>
        <div className="mt-4 space-y-2">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-emerald-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-slate-400">
              {isArabic ? 'البحث عن معلومات موثوقة' : 'Searching for reliable information'}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-slate-400">
              {isArabic ? 'جلب صور حقيقية ومناسبة' : 'Fetching real and relevant images'}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-slate-400">
              {isArabic ? 'البحث عن فيديوهات تعليمية' : 'Finding educational videos'}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-purple-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-slate-400">
              {isArabic ? 'جمع إحصائيات حديثة' : 'Gathering recent statistics'}
            </span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-slate-800 p-6 rounded-xl shadow-2xl ${isArabic ? 'font-arabic' : ''}`} dir={isArabic ? 'rtl' : 'ltr'}>
        <div className="text-center">
          <div className="text-yellow-400 text-4xl mb-4">⚠️</div>
          <p className="text-slate-300 mb-4">{error}</p>
          <div className="flex justify-center space-x-4">
            <Button onClick={onCreatePresentation} className="bg-sky-600 hover:bg-sky-500">
              {isArabic ? 'المتابعة بالمحتوى الأساسي' : 'Continue with Basic Content'}
            </Button>
            <Button onClick={onCancel} className="bg-slate-600 hover:bg-slate-500">
              {isArabic ? 'إلغاء' : 'Cancel'}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!contentData) {
    return null;
  }

  return (
    <div className={`bg-slate-800 p-6 rounded-xl shadow-2xl max-h-[80vh] overflow-y-auto ${isArabic ? 'font-arabic' : ''}`} dir={isArabic ? 'rtl' : 'ltr'}>
      <div className="mb-6">
        <h3 className="text-2xl font-bold text-sky-300 mb-2">
          {isArabic ? '🔍 معاينة المحتوى المحسن' : '🔍 Enhanced Content Preview'}
        </h3>
        <p className="text-slate-400">
          {isArabic ? 
            'تم العثور على محتوى حقيقي ومحدث. اختر العناصر التي تريد تضمينها في العرض التقديمي:' :
            'Found real and updated content. Select the elements you want to include in the presentation:'
          }
        </p>
      </div>

      {/* Information Sources */}
      {contentData.information.length > 0 && (
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-emerald-300 mb-3">
            📚 {isArabic ? 'مصادر المعلومات:' : 'Information Sources:'}
          </h4>
          <div className="space-y-2">
            {contentData.information.slice(0, 3).map((info, index) => (
              <div key={index} className="bg-slate-700 p-3 rounded-lg">
                <h5 className="font-medium text-slate-200">{info.title}</h5>
                <p className="text-sm text-slate-400 mt-1">{info.snippet}</p>
                <span className="text-xs text-slate-500">{info.source}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Statistics */}
      {contentData.statistics.length > 0 && (
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-blue-300 mb-3">
            📊 {isArabic ? 'إحصائيات حديثة:' : 'Recent Statistics:'}
          </h4>
          <div className="space-y-2">
            {contentData.statistics.slice(0, 3).map((stat, index) => (
              <div key={index} className="bg-blue-900/30 p-3 rounded-lg border border-blue-500/30">
                <p className="text-sm text-slate-300">📈 {stat}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Images */}
      {contentData.images.length > 0 && (
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-purple-300 mb-3">
            🖼️ {isArabic ? 'صور مقترحة:' : 'Suggested Images:'}
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {contentData.images.slice(0, 6).map((image, index) => (
              <div 
                key={index} 
                className={`relative cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                  selectedImages.has(index) ? 'border-purple-400 ring-2 ring-purple-400/50' : 'border-slate-600 hover:border-slate-500'
                }`}
                onClick={() => toggleImageSelection(index)}
              >
                <img 
                  src={image.url} 
                  alt={image.title}
                  className="w-full h-24 object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `https://source.unsplash.com/300x200/?${encodeURIComponent(image.searchTerms[0] || topic)}`;
                  }}
                />
                <div className="absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                  <span className="text-white text-xs text-center px-2">{image.title}</span>
                </div>
                {selectedImages.has(index) && (
                  <div className="absolute top-2 right-2 bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
                    ✓
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Videos */}
      {contentData.videos.length > 0 && (
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-red-300 mb-3">
            🎥 {isArabic ? 'فيديوهات تعليمية:' : 'Educational Videos:'}
          </h4>
          <div className="space-y-3">
            {contentData.videos.slice(0, 3).map((video, index) => (
              <div 
                key={index}
                className={`bg-slate-700 p-4 rounded-lg cursor-pointer border-2 transition-all ${
                  selectedVideos.has(index) ? 'border-red-400 ring-2 ring-red-400/50' : 'border-slate-600 hover:border-slate-500'
                }`}
                onClick={() => toggleVideoSelection(index)}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-16 h-12 bg-red-600 rounded flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <div className="flex-grow">
                    <h5 className="font-medium text-slate-200">{video.title}</h5>
                    {video.channel && (
                      <p className="text-sm text-slate-400">{video.channel}</p>
                    )}
                    {video.duration && (
                      <span className="text-xs text-slate-500">{video.duration}</span>
                    )}
                  </div>
                  {selectedVideos.has(index) && (
                    <div className="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
                      ✓
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between items-center pt-4 border-t border-slate-600">
        <Button onClick={onCancel} className="bg-slate-600 hover:bg-slate-500">
          {isArabic ? 'إلغاء' : 'Cancel'}
        </Button>
        <div className="text-center">
          <p className="text-sm text-slate-400 mb-2">
            {isArabic ? 
              `تم اختيار ${selectedImages.size} صور و ${selectedVideos.size} فيديوهات` :
              `Selected ${selectedImages.size} images and ${selectedVideos.size} videos`
            }
          </p>
        </div>
        <Button onClick={onCreatePresentation} className="bg-emerald-600 hover:bg-emerald-500">
          {isArabic ? 'إنشاء العرض التقديمي' : 'Create Presentation'}
        </Button>
      </div>
    </div>
  );
};
