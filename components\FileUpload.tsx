
import React, { useState, useCallback } from 'react';
import { parseFile } from '../services/fileParserService';
import { MAX_FILE_SIZE_MB } from '../constants';
import { PDFFlashExplorer } from './PDFFlashExplorer';
import { Button } from './common/Button';

// Helper function to detect Arabic text
const isArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
  const totalChars = text.replace(/\s/g, '').length;
  return arabicChars ? (arabicChars.length / totalChars) > 0.3 : false;
};

interface FileUploadProps {
  onFileProcessed: (text: string) => void;
  onTextSelect?: (text: string, pageNumber?: number) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const FileUpload: React.FC<FileUploadProps> = ({ onFileProcessed, onTextSelect, setIsLoading, setError }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileName, setFileName] = useState<string>('');
  const [showPDFExplorer, setShowPDFExplorer] = useState<boolean>(false);
  const [processedText, setProcessedText] = useState<string>('');

  // Detect if content is Arabic
  const isArabic = processedText ? isArabicText(processedText) : false;

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
        const errorMsg = isArabic ?
          `الملف كبير جداً. الحد الأقصى هو ${MAX_FILE_SIZE_MB} ميجابايت.` :
          `File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.`;
        setError(errorMsg);
        setSelectedFile(null);
        setFileName('');
        setShowPDFExplorer(false);
        event.target.value = ''; // Reset file input
        return;
      }
      setSelectedFile(file);
      setFileName(file.name);
      setShowPDFExplorer(false);
      setError(null); // Clear previous errors
    }
  };

  const handleSubmit = useCallback(async () => {
    if (!selectedFile) {
      const errorMsg = isArabic ?
        "يرجى اختيار ملف أولاً." :
        "Please select a file first.";
      setError(errorMsg);
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const textContent = await parseFile(selectedFile);
      setProcessedText(textContent);
      onFileProcessed(textContent);
    } catch (err) {
      console.error("Error processing file:", err);
      const errorMsg = isArabic ?
        `فشل في معالجة الملف: ${err instanceof Error ? err.message : String(err)}` :
        `Failed to process file: ${err instanceof Error ? err.message : String(err)}`;
      setError(errorMsg);
      onFileProcessed(''); // Clear previous content on error
    } finally {
      setIsLoading(false);
    }
  }, [selectedFile, onFileProcessed, setIsLoading, setError, isArabic]);

  // Handle PDF Flash Explorer
  const handleShowPDFExplorer = useCallback(() => {
    if (selectedFile && selectedFile.type === 'application/pdf') {
      setShowPDFExplorer(true);
    }
  }, [selectedFile]);

  // Handle text selection from PDF Explorer
  const handlePDFTextSelect = useCallback((text: string, pageNumber: number) => {
    onTextSelect?.(text, pageNumber);
    setShowPDFExplorer(false);
  }, [onTextSelect]);

  // Check if file is PDF
  const isPDF = selectedFile?.type === 'application/pdf';

  // Show PDF Flash Explorer if requested
  if (showPDFExplorer && selectedFile && isPDF) {
    return (
      <div className={`space-y-4 ${isArabic ? 'font-arabic' : ''}`} dir={isArabic ? 'rtl' : 'ltr'}>
        <PDFFlashExplorer
          pdfFile={selectedFile}
          onTextSelect={handlePDFTextSelect}
          onClose={() => setShowPDFExplorer(false)}
        />
      </div>
    );
  }

  return (
    <div className={`space-y-4 p-4 bg-slate-700 rounded-lg shadow-md card-enhanced ${isArabic ? 'font-arabic' : ''}`} dir={isArabic ? 'rtl' : 'ltr'}>
      <h2 className="text-xl font-semibold text-sky-300">
        {isArabic ? '📚 رفع الكتاب' : '📚 Upload Book'}
      </h2>

      <div>
        <label htmlFor="file-upload" className="block text-sm font-medium text-slate-300 mb-1">
          {isArabic ?
            'اختر ملف TXT أو PDF أو DOCX:' :
            'Select a TXT, PDF, or DOCX file:'
          }
        </label>
        <div className="mt-1 flex items-center space-x-2 space-x-reverse">
          <label className="w-full flex items-center px-4 py-2 bg-sky-600 text-white rounded-md shadow-sm tracking-wide border border-sky-700 cursor-pointer hover:bg-sky-500 hover:text-white transition-all duration-300 btn-enhanced">
            <svg className={`w-5 h-5 ${isArabic ? 'ml-2' : 'mr-2'}`} fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M16.88 9.1A4 4 0 0 1 16 17H5a5 5 0 0 1-1-9.9V7a3 3 0 0 1 4.52-2.59A4.98 4.98 0 0 1 17 8c0 .38-.04.74-.12 1.1zM11 11h3l-4-4-4 4h3v3h2v-3z" />
            </svg>
            <span className="text-sm leading-normal truncate flex-1">
              {fileName || (isArabic ? "اختر ملف..." : "Choose file...")}
            </span>
            <input
              id="file-upload"
              name="file-upload"
              type="file"
              className="sr-only"
              onChange={handleFileChange}
              accept=".txt,.pdf,.docx"
            />
          </label>
        </div>

        {fileName && (
          <div className="mt-2 space-y-2">
            <p className="text-xs text-slate-400">
              {isArabic ? `المحدد: ${fileName}` : `Selected: ${fileName}`}
            </p>

            {/* PDF Flash Explorer Button */}
            {isPDF && (
              <Button
                onClick={handleShowPDFExplorer}
                className="w-full bg-purple-600 hover:bg-purple-500 text-sm py-2"
              >
                ⚡ {isArabic ? 'فتح مستكشف PDF Flash' : 'Open PDF Flash Explorer'}
              </Button>
            )}
          </div>
        )}
      </div>

      <Button
        type="button"
        onClick={handleSubmit}
        disabled={!selectedFile}
        className="w-full bg-emerald-600 hover:bg-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed btn-enhanced"
      >
        {isArabic ? '⚙️ معالجة الملف' : '⚙️ Process File'}
      </Button>

      {/* File Type Info */}
      {selectedFile && (
        <div className="mt-3 p-3 bg-slate-600 rounded-md">
          <div className="flex items-center justify-between text-sm">
            <span className="text-slate-300">
              {isArabic ? 'نوع الملف:' : 'File Type:'}
            </span>
            <span className="text-sky-400 font-medium">
              {selectedFile.type || 'Unknown'}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm mt-1">
            <span className="text-slate-300">
              {isArabic ? 'الحجم:' : 'Size:'}
            </span>
            <span className="text-sky-400 font-medium">
              {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
            </span>
          </div>

          {isPDF && (
            <div className="mt-2 p-2 bg-purple-900/30 border border-purple-500/30 rounded text-xs">
              <div className="flex items-center space-x-2 space-x-reverse">
                <span className="text-purple-400">⚡</span>
                <span className="text-purple-300">
                  {isArabic ?
                    'ملف PDF متاح! يمكنك استخدام مستكشف PDF Flash للمعاينة السريعة والتفاعل المتقدم.' :
                    'PDF file detected! You can use PDF Flash Explorer for quick preview and advanced interaction.'
                  }
                </span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
