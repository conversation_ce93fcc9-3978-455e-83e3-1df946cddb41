
import React, { useState, useCallback } from 'react';
import { parseFile } from '../services/fileParserService';
import { MAX_FILE_SIZE_MB } from '../constants';

interface FileUploadProps {
  onFileProcessed: (text: string) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const FileUpload: React.FC<FileUploadProps> = ({ onFileProcessed, setIsLoading, setError }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileName, setFileName] = useState<string>('');

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
        setError(`الملف كبير جداً. الحد الأقصى للحجم هو ${MAX_FILE_SIZE_MB} ميجابايت.`);
        setSelectedFile(null);
        setFileName('');
        event.target.value = ''; // Reset file input
        return;
      }
      setSelectedFile(file);
      setFileName(file.name);
      setError(null); // Clear previous errors
    }
  };

  const handleSubmit = useCallback(async () => {
    if (!selectedFile) {
      setError("يرجى اختيار ملف أولاً.");
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const textContent = await parseFile(selectedFile);
      onFileProcessed(textContent);
    } catch (err) {
      console.error("Error processing file:", err);
      setError(`فشل في معالجة الملف: ${err instanceof Error ? err.message : String(err)}`);
      onFileProcessed(''); // Clear previous content on error
    } finally {
      setIsLoading(false);
    }
  }, [selectedFile, onFileProcessed, setIsLoading, setError]);

  return (
    <div className="space-y-4 p-4 bg-slate-700 rounded-lg shadow-md" dir="rtl">
      <h2 className="text-xl font-semibold text-sky-300 font-arabic">رفع الكتاب</h2>
      <div>
        <label htmlFor="file-upload" className="block text-sm font-medium text-slate-300 mb-1 font-arabic">
          اختر ملف TXT أو PDF أو DOCX:
        </label>
        <div className="mt-1 flex items-center space-x-2 space-x-reverse">
            <label className="w-full flex items-center px-4 py-2 bg-sky-600 text-white rounded-md shadow-sm tracking-wide border border-sky-700 cursor-pointer hover:bg-sky-500 hover:text-white font-arabic">
                <svg className="w-5 h-5 ml-2" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M16.88 9.1A4 4 0 0 1 16 17H5a5 5 0 0 1-1-9.9V7a3 3 0 0 1 4.52-2.59A4.98 4.98 0 0 1 17 8c0 .38-.04.74-.12 1.1zM11 11h3l-4-4-4 4h3v3h2v-3z" />
                </svg>
                <span className="text-sm leading-normal truncate" style={{maxWidth: 'calc(100% - 30px)'}}>{fileName || "اختر ملف..."}</span>
                <input id="file-upload" name="file-upload" type="file" className="sr-only" onChange={handleFileChange} accept=".txt,.pdf,.docx" />
            </label>
        </div>
         {fileName && <p className="text-xs text-slate-400 mt-1 font-arabic">المحدد: {fileName}</p>}
      </div>
      <button
        onClick={handleSubmit}
        disabled={!selectedFile}
        className="w-full px-4 py-2 bg-emerald-600 text-white font-semibold rounded-md hover:bg-emerald-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:ring-opacity-75 disabled:opacity-50 disabled:cursor-not-allowed font-arabic"
      >
        معالجة الملف
      </button>
    </div>
  );
};
