// مستكشف PDF Flash المتقدم - PDF Flash Explorer Component
// يوفر معاينة سريعة وتفاعلية لملفات PDF مع ميزات متقدمة

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from './common/Button';
import { QuickTTS } from './QuickTTS';
import { TTSReader } from './TTSReader';

// Helper function to detect Arabic text
const isArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFdff\uFE70-\uFEFF]/g);
  const totalChars = text.replace(/\s/g, '').length;
  return arabicChars ? (arabicChars.length / totalChars) > 0.3 : false;
};

interface PDFPage {
  pageNumber: number;
  canvas: HTMLCanvasElement;
  thumbnail: HTMLCanvasElement;
  text: string;
  isLoaded: boolean;
}

interface PDFFlashExplorerProps {
  pdfFile: File;
  onTextSelect?: (text: string, pageNumber: number) => void;
  onClose?: () => void;
}

export const PDFFlashExplorer: React.FC<PDFFlashExplorerProps> = ({
  pdfFile,
  onTextSelect,
  onClose
}) => {
  const [pdfDoc, setPdfDoc] = useState<any>(null);
  const [pages, setPages] = useState<PDFPage[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1.0);
  const [selectedText, setSelectedText] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchResults, setSearchResults] = useState<{page: number, text: string}[]>([]);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const thumbnailsRef = useRef<HTMLDivElement>(null);

  // Detect if content is Arabic
  const isArabic = pages.length > 0 && isArabicText(pages[0]?.text || '');

  // Load PDF document
  useEffect(() => {
    const loadPDF = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Check if pdfjsLib is available
        if (typeof window.pdfjsLib === 'undefined') {
          throw new Error('PDF.js library not loaded');
        }

        const arrayBuffer = await pdfFile.arrayBuffer();
        const pdf = await window.pdfjsLib.getDocument({ data: arrayBuffer }).promise;

        setPdfDoc(pdf);
        setTotalPages(pdf.numPages);

        // Initialize pages array
        const pagesArray: PDFPage[] = [];
        for (let i = 1; i <= pdf.numPages; i++) {
          pagesArray.push({
            pageNumber: i,
            canvas: document.createElement('canvas'),
            thumbnail: document.createElement('canvas'),
            text: '',
            isLoaded: false
          });
        }
        setPages(pagesArray);

        // Load first page immediately
        await loadPage(pdf, 1, pagesArray);

        // Load thumbnails for all pages
        loadAllThumbnails(pdf, pagesArray);

      } catch (err) {
        console.error('Error loading PDF:', err);
        setError(isArabic ?
          'حدث خطأ في تحميل ملف PDF. يرجى التأكد من صحة الملف.' :
          'Error loading PDF file. Please ensure the file is valid.'
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadPDF();
  }, [pdfFile, isArabic]);

  // Load a specific page
  const loadPage = useCallback(async (pdf: any, pageNum: number, pagesArray: PDFPage[]) => {
    try {
      const page = await pdf.getPage(pageNum);
      const viewport = page.getViewport({ scale: zoomLevel });

      const canvas = pagesArray[pageNum - 1].canvas;
      const context = canvas.getContext('2d');

      canvas.height = viewport.height;
      canvas.width = viewport.width;

      const renderContext = {
        canvasContext: context,
        viewport: viewport
      };

      await page.render(renderContext).promise;

      // Extract text content
      const textContent = await page.getTextContent();
      const text = textContent.items.map((item: any) => item.str).join(' ');

      // Update page data
      const updatedPages = [...pagesArray];
      updatedPages[pageNum - 1] = {
        ...updatedPages[pageNum - 1],
        text,
        isLoaded: true
      };
      setPages(updatedPages);

      // Update canvas display
      if (pageNum === currentPage && canvasRef.current) {
        const displayContext = canvasRef.current.getContext('2d');
        canvasRef.current.width = canvas.width;
        canvasRef.current.height = canvas.height;
        displayContext?.drawImage(canvas, 0, 0);
      }

    } catch (err) {
      console.error(`Error loading page ${pageNum}:`, err);
    }
  }, [zoomLevel, currentPage]);

  // Load thumbnails for all pages
  const loadAllThumbnails = useCallback(async (pdf: any, pagesArray: PDFPage[]) => {
    const thumbnailPromises = [];

    for (let i = 1; i <= pdf.numPages; i++) {
      thumbnailPromises.push(loadThumbnail(pdf, i, pagesArray));
    }

    await Promise.all(thumbnailPromises);
  }, []);

  // Load thumbnail for a specific page
  const loadThumbnail = useCallback(async (pdf: any, pageNum: number, pagesArray: PDFPage[]) => {
    try {
      const page = await pdf.getPage(pageNum);
      const viewport = page.getViewport({ scale: 0.2 }); // Small scale for thumbnail

      const thumbnail = pagesArray[pageNum - 1].thumbnail;
      const context = thumbnail.getContext('2d');

      thumbnail.height = viewport.height;
      thumbnail.width = viewport.width;

      const renderContext = {
        canvasContext: context,
        viewport: viewport
      };

      await page.render(renderContext).promise;

    } catch (err) {
      console.error(`Error loading thumbnail ${pageNum}:`, err);
    }
  }, []);

  // Navigate to specific page
  const goToPage = useCallback(async (pageNum: number) => {
    if (pageNum < 1 || pageNum > totalPages || !pdfDoc) return;

    setCurrentPage(pageNum);

    if (!pages[pageNum - 1]?.isLoaded) {
      await loadPage(pdfDoc, pageNum, pages);
    } else {
      // Display already loaded page
      const canvas = pages[pageNum - 1].canvas;
      if (canvasRef.current && canvas) {
        const displayContext = canvasRef.current.getContext('2d');
        canvasRef.current.width = canvas.width;
        canvasRef.current.height = canvas.height;
        displayContext?.drawImage(canvas, 0, 0);
      }
    }
  }, [totalPages, pdfDoc, pages, loadPage]);

  // Handle zoom change
  const handleZoomChange = useCallback(async (newZoom: number) => {
    setZoomLevel(newZoom);
    if (pdfDoc) {
      await loadPage(pdfDoc, currentPage, pages);
    }
  }, [pdfDoc, currentPage, pages, loadPage]);

  // Search in PDF
  const handleSearch = useCallback(() => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    const results: {page: number, text: string}[] = [];
    pages.forEach((page, index) => {
      if (page.text.toLowerCase().includes(searchQuery.toLowerCase())) {
        results.push({
          page: index + 1,
          text: page.text.substring(0, 100) + '...'
        });
      }
    });

    setSearchResults(results);
  }, [searchQuery, pages]);

  // Handle text selection
  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      const text = selection.toString().trim();
      setSelectedText(text);
      onTextSelect?.(text, currentPage);
    }
  }, [currentPage, onTextSelect]);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    if (!isFullscreen) {
      containerRef.current?.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-96 ${isArabic ? 'font-arabic' : ''}`} dir={isArabic ? 'rtl' : 'ltr'}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-400 mx-auto mb-4"></div>
          <p className="text-slate-300">
            {isArabic ? 'جاري تحميل مستكشف PDF Flash...' : 'Loading PDF Flash Explorer...'}
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-900/20 border border-red-500/30 rounded-lg p-6 ${isArabic ? 'font-arabic' : ''}`} dir={isArabic ? 'rtl' : 'ltr'}>
        <div className="flex items-center space-x-3">
          <div className="text-red-400 text-2xl">❌</div>
          <div>
            <h3 className="text-red-300 font-semibold">
              {isArabic ? 'خطأ في تحميل PDF' : 'PDF Loading Error'}
            </h3>
            <p className="text-red-200 text-sm mt-1">{error}</p>
          </div>
        </div>
        {onClose && (
          <Button onClick={onClose} className="mt-4 bg-slate-600 hover:bg-slate-500">
            {isArabic ? 'إغلاق' : 'Close'}
          </Button>
        )}
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`pdf-flash-container ${isArabic ? 'font-arabic' : ''} ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}
      dir={isArabic ? 'rtl' : 'ltr'}
    >
      {/* Header Controls */}
      <div className="pdf-controls flex items-center justify-between flex-wrap gap-4 p-4">
        <div className="flex items-center space-x-4 space-x-reverse">
          <h3 className="text-lg font-semibold text-sky-300">
            📄 {isArabic ? 'مستكشف PDF Flash' : 'PDF Flash Explorer'}
          </h3>
          <span className="text-sm text-slate-400">
            {isArabic ?
              `الصفحة ${currentPage} من ${totalPages}` :
              `Page ${currentPage} of ${totalPages}`
            }
          </span>
        </div>

        <div className="flex items-center space-x-2 space-x-reverse">
          {/* Zoom Controls */}
          <Button
            onClick={() => handleZoomChange(Math.max(0.5, zoomLevel - 0.25))}
            className="bg-slate-600 hover:bg-slate-500 text-sm px-3 py-1"
          >
            🔍-
          </Button>
          <span className="text-sm text-slate-300 px-2">
            {Math.round(zoomLevel * 100)}%
          </span>
          <Button
            onClick={() => handleZoomChange(Math.min(3.0, zoomLevel + 0.25))}
            className="bg-slate-600 hover:bg-slate-500 text-sm px-3 py-1"
          >
            🔍+
          </Button>

          {/* Fullscreen Toggle */}
          <Button
            onClick={toggleFullscreen}
            className="bg-slate-600 hover:bg-slate-500 text-sm px-3 py-1"
          >
            {isFullscreen ? '🗗' : '🗖'}
          </Button>

          {/* Close Button */}
          {onClose && (
            <Button
              onClick={onClose}
              className="bg-red-600 hover:bg-red-500 text-sm px-3 py-1"
            >
              ✕
            </Button>
          )}
        </div>
      </div>

      {/* Search Bar */}
      <div className="px-4 pb-4">
        <div className="flex items-center space-x-2 space-x-reverse">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            placeholder={isArabic ? 'البحث في PDF...' : 'Search in PDF...'}
            className="flex-1 bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-sm text-slate-200 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500"
          />
          <Button
            onClick={handleSearch}
            className="bg-sky-600 hover:bg-sky-500 text-sm px-4 py-2"
          >
            🔍
          </Button>
        </div>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <div className="mt-2 max-h-32 overflow-y-auto">
            {searchResults.map((result, index) => (
              <div
                key={index}
                onClick={() => goToPage(result.page)}
                className="cursor-pointer hover:bg-slate-700 p-2 rounded text-sm border-b border-slate-600 last:border-b-0"
              >
                <span className="text-sky-400">
                  {isArabic ? `الصفحة ${result.page}:` : `Page ${result.page}:`}
                </span>
                <span className="text-slate-300 ml-2">{result.text}</span>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="flex flex-col lg:flex-row h-full">
        {/* Main PDF Viewer */}
        <div className="flex-1 p-4">
          <div className="bg-white rounded-lg p-4 shadow-lg overflow-auto max-h-[600px]">
            <canvas
              ref={canvasRef}
              className="pdf-page-canvas mx-auto"
              onMouseUp={handleTextSelection}
            />
          </div>

          {/* Navigation Controls */}
          <div className="flex items-center justify-center space-x-4 space-x-reverse mt-4">
            <Button
              onClick={() => goToPage(currentPage - 1)}
              disabled={currentPage <= 1}
              className="bg-sky-600 hover:bg-sky-500 disabled:bg-slate-600"
            >
              {isArabic ? '← السابق' : '← Previous'}
            </Button>

            <div className="flex items-center space-x-2 space-x-reverse">
              <input
                type="number"
                min="1"
                max={totalPages}
                value={currentPage}
                onChange={(e) => {
                  const page = parseInt(e.target.value);
                  if (page >= 1 && page <= totalPages) {
                    goToPage(page);
                  }
                }}
                placeholder={isArabic ? 'رقم الصفحة' : 'Page number'}
                aria-label={isArabic ? 'رقم الصفحة' : 'Page number'}
                className="w-16 bg-slate-700 border border-slate-600 rounded px-2 py-1 text-center text-sm"
              />
              <span className="text-slate-400 text-sm">/ {totalPages}</span>
            </div>

            <Button
              onClick={() => goToPage(currentPage + 1)}
              disabled={currentPage >= totalPages}
              className="bg-sky-600 hover:bg-sky-500 disabled:bg-slate-600"
            >
              {isArabic ? 'التالي →' : 'Next →'}
            </Button>
          </div>
        </div>

        {/* Thumbnails Sidebar */}
        <div className="w-full lg:w-48 p-4 border-t lg:border-t-0 lg:border-l border-slate-600">
          <h4 className="text-sm font-semibold text-slate-300 mb-3">
            {isArabic ? 'الصفحات' : 'Pages'}
          </h4>
          <div
            ref={thumbnailsRef}
            className="grid grid-cols-4 lg:grid-cols-1 gap-2 max-h-[500px] overflow-y-auto"
          >
            {pages.map((page, index) => (
              <div
                key={index}
                onClick={() => goToPage(index + 1)}
                className={`pdf-thumbnail cursor-pointer ${
                  currentPage === index + 1 ? 'active' : ''
                }`}
              >
                <canvas
                  ref={(el) => {
                    if (el && page.thumbnail) {
                      const ctx = el.getContext('2d');
                      el.width = page.thumbnail.width;
                      el.height = page.thumbnail.height;
                      ctx?.drawImage(page.thumbnail, 0, 0);
                    }
                  }}
                  className="w-full h-full object-cover"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white text-xs text-center py-1">
                  {index + 1}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Selected Text Display */}
      {selectedText && (
        <div className="pdf-controls border-t border-slate-600 p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-semibold text-emerald-300">
              {isArabic ? 'النص المحدد:' : 'Selected Text:'}
            </h4>
            <QuickTTS
              text={selectedText}
              size="sm"
              className="opacity-80 hover:opacity-100"
            />
          </div>
          <div className="bg-slate-700 rounded-md p-3 text-sm text-slate-200 max-h-24 overflow-y-auto" dir={isArabic ? 'rtl' : 'ltr'}>
            {selectedText}
          </div>
          <div className="flex items-center space-x-2 space-x-reverse mt-2">
            <Button
              onClick={() => onTextSelect?.(selectedText, currentPage)}
              className="bg-emerald-600 hover:bg-emerald-500 text-sm px-3 py-1"
            >
              {isArabic ? 'تحليل هذا النص' : 'Analyze This Text'}
            </Button>
            <Button
              onClick={() => setSelectedText('')}
              className="bg-slate-600 hover:bg-slate-500 text-sm px-3 py-1"
            >
              {isArabic ? 'مسح' : 'Clear'}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
