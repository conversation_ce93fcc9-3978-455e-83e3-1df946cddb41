
import React, { useState, useEffect } from 'react';
import type { Presentation, Slide, InteractionType } from '../types';
import { MermaidDiagram } from './MermaidDiagram';
import { Button } from './common/Button';

// Helper function to detect Arabic text
const isArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
  const totalChars = text.replace(/\s/g, '').length;
  return arabicChars ? (arabicChars.length / totalChars) > 0.3 : false;
};

interface PresentationViewerProps {
  presentation: Presentation;
}

const InteractiveElement: React.FC<{ interaction: InteractionType }> = ({ interaction }) => {
  const [userAnswer, setUserAnswer] = useState<string>('');
  const [showResult, setShowResult] = useState<boolean>(false);

  if (interaction.type === 'video' && interaction.videoUrl) {
    return (
      <div className="my-4">
        <h4 className="text-md font-semibold text-sky-300 mb-2">Related Video:</h4>
        <video controls width="100%" className="rounded-md">
          <source src={interaction.videoUrl} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      </div>
    );
  }

  if (interaction.type === 'quiz' && interaction.question && interaction.options) {
    const handleSubmit = () => {
      setShowResult(true);
    };
    return (
      <div className="my-4 p-4 bg-slate-700 rounded-lg">
        <h4 className="text-md font-semibold text-sky-300 mb-2">Quiz: {interaction.question}</h4>
        <div className="space-y-2">
          {interaction.options.map((option, index) => (
            <label key={index} className="flex items-center space-x-2 p-2 rounded hover:bg-slate-600 cursor-pointer">
              <input type="radio" name="quizOption" value={option} onChange={(e) => setUserAnswer(e.target.value)} className="form-radio text-sky-500 bg-slate-800 border-slate-600 focus:ring-sky-500" />
              <span>{option}</span>
            </label>
          ))}
        </div>
        <Button onClick={handleSubmit} className="mt-3 bg-emerald-600 hover:bg-emerald-500 text-sm">Submit Answer</Button>
        {showResult && (
          <p className={`mt-2 text-sm font-medium ${userAnswer === interaction.correctAnswer ? 'text-green-400' : 'text-red-400'}`}>
            {userAnswer === interaction.correctAnswer ? 'Correct!' : `Incorrect. The answer is ${interaction.correctAnswer}.`}
          </p>
        )}
      </div>
    );
  }
  return null;
};


export const PresentationViewer: React.FC<PresentationViewerProps> = ({ presentation }) => {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);

  // Detect if presentation content is Arabic
  const isArabicPresentation = presentation && presentation.slides.length > 0 &&
    isArabicText(presentation.title + " " + presentation.slides[0].slideTitle);

  const goToNextSlide = () => {
    setCurrentSlideIndex((prev) => Math.min(prev + 1, presentation.slides.length - 1));
  };

  const goToPrevSlide = () => {
    setCurrentSlideIndex((prev) => Math.max(prev - 1, 0));
  };

  useEffect(() => {
    setCurrentSlideIndex(0); // Reset to first slide when presentation changes
  }, [presentation]);

  if (!presentation || presentation.slides.length === 0) {
    return (
      <p className={`text-slate-400 ${isArabicPresentation ? 'font-arabic' : ''}`} dir={isArabicPresentation ? 'rtl' : 'ltr'}>
        {isArabicPresentation ? 'لم يتم تحميل عرض تقديمي أو العرض فارغ.' : 'No presentation loaded or presentation is empty.'}
      </p>
    );
  }

  const currentSlide: Slide = presentation.slides[currentSlideIndex];

  return (
    <div className={`space-y-6 ${isArabicPresentation ? 'font-arabic' : ''}`} dir={isArabicPresentation ? 'rtl' : 'ltr'}>
      <h2 className="text-3xl font-bold text-sky-300 text-center">{presentation.title}</h2>

      <div className="bg-slate-700 p-6 rounded-lg shadow-xl min-h-[400px] flex flex-col justify-between">
        <div>
          <h3 className="text-2xl font-semibold text-cyan-300 mb-4">{currentSlide.slideTitle}</h3>
          <ul className={`list-disc ${isArabicPresentation ? 'list-inside' : 'list-inside'} space-y-2 text-slate-200 mb-4`}>
            {currentSlide.contentPoints.map((point, index) => (
              <li key={index}>{point}</li>
            ))}
          </ul>
          {currentSlide.imageUrl && (
            <div className="my-4">
              <img src={currentSlide.imageUrl} alt={currentSlide.imageSuggestion || currentSlide.slideTitle} className="max-w-full md:max-w-md mx-auto rounded-lg shadow-md" />
            </div>
          )}
          {currentSlide.mermaidSyntax && (
            <MermaidDiagram chart={currentSlide.mermaidSyntax} id={`mermaid-${currentSlideIndex}`} />
          )}
          {currentSlide.interaction && <InteractiveElement interaction={currentSlide.interaction} />}
        </div>

        <div className={`mt-6 flex ${isArabicPresentation ? 'flex-row-reverse' : ''} justify-between items-center border-t border-slate-600 pt-4`}>
          <Button onClick={goToPrevSlide} disabled={currentSlideIndex === 0} className="bg-sky-600 hover:bg-sky-500">
            {isArabicPresentation ? 'السابق' : 'Previous'}
          </Button>
          <p className="text-slate-400 text-sm">
            {isArabicPresentation
              ? `الشريحة ${currentSlideIndex + 1} من ${presentation.slides.length}`
              : `Slide ${currentSlideIndex + 1} of ${presentation.slides.length}`
            }
          </p>
          <Button onClick={goToNextSlide} disabled={currentSlideIndex === presentation.slides.length - 1} className="bg-sky-600 hover:bg-sky-500">
            {isArabicPresentation ? 'التالي' : 'Next'}
          </Button>
        </div>

        {/* Slide indicators */}
        <div className="flex justify-center mt-4 space-x-1 rtl:space-x-reverse">
          {presentation.slides.map((_, index) => (
            <button
              key={index}
              type="button"
              onClick={() => setCurrentSlideIndex(index)}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentSlideIndex ? 'bg-sky-400' : 'bg-slate-600 hover:bg-slate-500'
              }`}
              aria-label={isArabicPresentation ? `الذهاب للشريحة ${index + 1}` : `Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
