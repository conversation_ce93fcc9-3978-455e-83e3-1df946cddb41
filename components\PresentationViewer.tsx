
import React, { useState, useEffect } from 'react';
import type { Presentation, Slide, InteractionType } from '../types';
import { MermaidDiagram } from './MermaidDiagram';
import { Button } from './common/Button';
import { QuickTTS } from './QuickTTS';
import { TTSReader } from './TTSReader';

interface PresentationViewerProps {
  presentation: Presentation;
}

const InteractiveElement: React.FC<{ interaction: InteractionType }> = ({ interaction }) => {
  const [userAnswer, setUserAnswer] = useState<string>('');
  const [showResult, setShowResult] = useState<boolean>(false);

  if (interaction.type === 'video' && interaction.videoUrl) {
    return (
      <div className="my-4">
        <h4 className="text-md font-semibold text-sky-300 mb-2">Related Video:</h4>
        <video controls width="100%" className="rounded-md">
          <source src={interaction.videoUrl} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      </div>
    );
  }

  if (interaction.type === 'quiz' && interaction.question && interaction.options) {
    const handleSubmit = () => {
      setShowResult(true);
    };
    return (
      <div className="my-4 p-4 bg-slate-700 rounded-lg">
        <h4 className="text-md font-semibold text-sky-300 mb-2">Quiz: {interaction.question}</h4>
        <div className="space-y-2">
          {interaction.options.map((option, index) => (
            <label key={index} className="flex items-center space-x-2 p-2 rounded hover:bg-slate-600 cursor-pointer">
              <input type="radio" name="quizOption" value={option} onChange={(e) => setUserAnswer(e.target.value)} className="form-radio text-sky-500 bg-slate-800 border-slate-600 focus:ring-sky-500" />
              <span>{option}</span>
            </label>
          ))}
        </div>
        <Button onClick={handleSubmit} className="mt-3 bg-emerald-600 hover:bg-emerald-500 text-sm">Submit Answer</Button>
        {showResult && (
          <p className={`mt-2 text-sm font-medium ${userAnswer === interaction.correctAnswer ? 'text-green-400' : 'text-red-400'}`}>
            {userAnswer === interaction.correctAnswer ? 'Correct!' : `Incorrect. The answer is ${interaction.correctAnswer}.`}
          </p>
        )}
      </div>
    );
  }
  return null;
};


export const PresentationViewer: React.FC<PresentationViewerProps> = ({ presentation }) => {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);

  const goToNextSlide = () => {
    setCurrentSlideIndex((prev) => Math.min(prev + 1, presentation.slides.length - 1));
  };

  const goToPrevSlide = () => {
    setCurrentSlideIndex((prev) => Math.max(prev - 1, 0));
  };

  useEffect(() => {
    setCurrentSlideIndex(0); // Reset to first slide when presentation changes
  }, [presentation]);

  if (!presentation || presentation.slides.length === 0) {
    return <p className="text-slate-400">No presentation loaded or presentation is empty.</p>;
  }

  const currentSlide: Slide = presentation.slides[currentSlideIndex];

  return (
    <div className="space-y-6">
      <div className="text-center space-y-3">
        <h2 className="text-3xl font-bold text-sky-300">{presentation.title}</h2>
        <QuickTTS
          text={presentation.title}
          size="sm"
          className="inline-flex opacity-80 hover:opacity-100"
        />
      </div>

      <div className="bg-slate-700 p-6 rounded-lg shadow-xl min-h-[400px] flex flex-col justify-between">
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-2xl font-semibold text-cyan-300">{currentSlide.slideTitle}</h3>
            <QuickTTS
              text={currentSlide.slideTitle + '. ' + currentSlide.contentPoints.join('. ')}
              size="sm"
              className="opacity-80 hover:opacity-100"
            />
          </div>
          <ul className="list-disc list-inside space-y-2 text-slate-200 mb-4">
            {currentSlide.contentPoints.map((point, index) => (
              <li key={index}>{point}</li>
            ))}
          </ul>
          {currentSlide.imageUrl && (
            <div className="my-4">
              <img src={currentSlide.imageUrl} alt={currentSlide.imageSuggestion || currentSlide.slideTitle} className="max-w-full md:max-w-md mx-auto rounded-lg shadow-md" />
            </div>
          )}
          {currentSlide.mermaidSyntax && (
            <MermaidDiagram chart={currentSlide.mermaidSyntax} id={`mermaid-${currentSlideIndex}`} />
          )}
          {currentSlide.interaction && <InteractiveElement interaction={currentSlide.interaction} />}
        </div>

        <div className="mt-6 flex justify-between items-center border-t border-slate-600 pt-4">
          <Button onClick={goToPrevSlide} disabled={currentSlideIndex === 0} className="bg-sky-600 hover:bg-sky-500">
            Previous
          </Button>
          <p className="text-slate-400 text-sm">
            Slide {currentSlideIndex + 1} of {presentation.slides.length}
          </p>
          <Button onClick={goToNextSlide} disabled={currentSlideIndex === presentation.slides.length - 1} className="bg-sky-600 hover:bg-sky-500">
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};
