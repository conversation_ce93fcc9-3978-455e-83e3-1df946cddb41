
import React, { useState, useEffect } from 'react';
import type { Presentation, Slide, InteractionType } from '../types';
import { MermaidDiagram } from './MermaidDiagram';
import { Button } from './common/Button';

interface PresentationViewerProps {
  presentation: Presentation;
}

const InteractiveElement: React.FC<{ interaction: InteractionType }> = ({ interaction }) => {
  const [userAnswer, setUserAnswer] = useState<string>('');
  const [showResult, setShowResult] = useState<boolean>(false);

  if (interaction.type === 'video' && interaction.videoUrl) {
    return (
      <div className="my-4">
        <h4 className="text-md font-semibold text-sky-300 mb-2">Related Video:</h4>
        <video controls width="100%" className="rounded-md">
          <source src={interaction.videoUrl} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      </div>
    );
  }

  if (interaction.type === 'quiz' && interaction.question && interaction.options) {
    const handleSubmit = () => {
      setShowResult(true);
    };
    return (
      <div className="my-4 p-4 bg-slate-700 rounded-lg">
        <h4 className="text-md font-semibold text-sky-300 mb-2">Quiz: {interaction.question}</h4>
        <div className="space-y-2">
          {interaction.options.map((option, index) => (
            <label key={index} className="flex items-center space-x-2 p-2 rounded hover:bg-slate-600 cursor-pointer">
              <input type="radio" name="quizOption" value={option} onChange={(e) => setUserAnswer(e.target.value)} className="form-radio text-sky-500 bg-slate-800 border-slate-600 focus:ring-sky-500" />
              <span>{option}</span>
            </label>
          ))}
        </div>
        <Button onClick={handleSubmit} className="mt-3 bg-emerald-600 hover:bg-emerald-500 text-sm">Submit Answer</Button>
        {showResult && (
          <p className={`mt-2 text-sm font-medium ${userAnswer === interaction.correctAnswer ? 'text-green-400' : 'text-red-400'}`}>
            {userAnswer === interaction.correctAnswer ? 'Correct!' : `Incorrect. The answer is ${interaction.correctAnswer}.`}
          </p>
        )}
      </div>
    );
  }
  return null;
};


export const PresentationViewer: React.FC<PresentationViewerProps> = ({ presentation }) => {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);

  // Detect if presentation content is Arabic
  const isArabicPresentation = presentation && presentation.slides.length > 0 &&
    isArabicText(presentation.title + " " + presentation.slides[0].slideTitle);

  const goToNextSlide = () => {
    setCurrentSlideIndex((prev) => Math.min(prev + 1, presentation.slides.length - 1));
  };

  const goToPrevSlide = () => {
    setCurrentSlideIndex((prev) => Math.max(prev - 1, 0));
  };

  useEffect(() => {
    setCurrentSlideIndex(0); // Reset to first slide when presentation changes
  }, [presentation]);

  if (!presentation || presentation.slides.length === 0) {
    return (
      <p className={`text-slate-400 ${isArabicPresentation ? 'font-arabic' : ''}`} dir={isArabicPresentation ? 'rtl' : 'ltr'}>
        {isArabicPresentation ? 'لم يتم تحميل عرض تقديمي أو العرض فارغ.' : 'No presentation loaded or presentation is empty.'}
      </p>
    );
  }

  const currentSlide: Slide = presentation.slides[currentSlideIndex];

  return (
    <div className={`space-y-6 ${isArabicPresentation ? 'font-arabic' : ''}`} dir={isArabicPresentation ? 'rtl' : 'ltr'}>
      <h2 className="text-3xl font-bold text-sky-300 text-center">{presentation.title}</h2>

      <div className="bg-slate-700 p-6 rounded-lg shadow-xl min-h-[500px] flex flex-col justify-between">
        <div>
          <h3 className="text-2xl font-semibold text-cyan-300 mb-4">{currentSlide.slideTitle}</h3>

          {/* Main Content Points */}
          <ul className={`list-disc space-y-2 text-slate-200 mb-4 ${isArabicPresentation ? 'list-inside pr-4' : 'list-inside pl-4'}`}>
            {currentSlide.contentPoints.map((point, index) => (
              <li key={index} className="leading-relaxed">{point}</li>
            ))}
          </ul>

          {/* Real World Examples */}
          {currentSlide.realWorldExamples && currentSlide.realWorldExamples.length > 0 && (
            <div className="my-4 p-4 bg-slate-600 rounded-lg">
              <h4 className="text-lg font-semibold text-emerald-300 mb-2">
                {isArabicPresentation ? '🌍 أمثلة من العالم الحقيقي:' : '🌍 Real World Examples:'}
              </h4>
              <ul className={`space-y-1 text-slate-300 ${isArabicPresentation ? 'list-inside pr-4' : 'list-inside pl-4'}`}>
                {currentSlide.realWorldExamples.map((example, index) => (
                  <li key={index} className="text-sm">• {example}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Statistics */}
          {currentSlide.statistics && currentSlide.statistics.length > 0 && (
            <div className="my-4 p-4 bg-blue-900/30 rounded-lg border border-blue-500/30">
              <h4 className="text-lg font-semibold text-blue-300 mb-2">
                {isArabicPresentation ? '📊 إحصائيات وبيانات:' : '📊 Statistics & Data:'}
              </h4>
              <ul className="space-y-1 text-slate-300">
                {currentSlide.statistics.map((stat, index) => (
                  <li key={index} className="text-sm font-medium">📈 {stat}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Image */}
          {currentSlide.imageUrl && (
            <div className="my-4">
              <img
                src={currentSlide.imageUrl}
                alt={currentSlide.imageSuggestion || currentSlide.slideTitle}
                className="max-w-full md:max-w-md mx-auto rounded-lg shadow-md border border-slate-600"
                onError={(e) => {
                  // Fallback to a different image source if the original fails
                  const target = e.target as HTMLImageElement;
                  if (currentSlide.searchTerms && currentSlide.searchTerms.length > 0) {
                    target.src = `https://source.unsplash.com/800x600/?${encodeURIComponent(currentSlide.searchTerms[0])}`;
                  }
                }}
              />
              {currentSlide.imageSuggestion && (
                <p className="text-xs text-slate-500 text-center mt-2">{currentSlide.imageSuggestion}</p>
              )}
            </div>
          )}

          {/* Mermaid Diagram */}
          {currentSlide.mermaidSyntax && (
            <div className="my-4">
              <MermaidDiagram chart={currentSlide.mermaidSyntax} id={`mermaid-${currentSlideIndex}`} />
              {currentSlide.diagramSuggestion?.description && (
                <p className="text-xs text-slate-500 text-center mt-2">{currentSlide.diagramSuggestion.description}</p>
              )}
            </div>
          )}

          {/* Interactive Element */}
          {currentSlide.interaction && (
            <InteractiveElement interaction={currentSlide.interaction} isArabic={isArabicPresentation} />
          )}

          {/* Sources */}
          {currentSlide.sources && currentSlide.sources.length > 0 && (
            <div className="my-4 p-3 bg-slate-800 rounded-lg border border-slate-600">
              <h4 className="text-sm font-semibold text-slate-400 mb-2">
                {isArabicPresentation ? '📚 المصادر:' : '📚 Sources:'}
              </h4>
              <ul className="space-y-1">
                {currentSlide.sources.map((source, index) => (
                  <li key={index} className="text-xs text-slate-500">• {source}</li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <div className={`mt-6 flex justify-between items-center border-t border-slate-600 pt-4 ${isArabicPresentation ? 'flex-row-reverse' : ''}`}>
          <Button
            onClick={goToPrevSlide}
            disabled={currentSlideIndex === 0}
            className="bg-sky-600 hover:bg-sky-500 flex items-center space-x-2"
          >
            {!isArabicPresentation && (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            )}
            <span>{isArabicPresentation ? 'السابق' : 'Previous'}</span>
            {isArabicPresentation && (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            )}
          </Button>

          <div className="text-center">
            <p className="text-slate-400 text-sm">
              {isArabicPresentation ?
                `الشريحة ${currentSlideIndex + 1} من ${presentation.slides.length}` :
                `Slide ${currentSlideIndex + 1} of ${presentation.slides.length}`
              }
            </p>
            <div className="flex justify-center mt-2 space-x-1">
              {presentation.slides.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSlideIndex(index)}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentSlideIndex ? 'bg-sky-400' : 'bg-slate-600 hover:bg-slate-500'
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          </div>

          <Button
            onClick={goToNextSlide}
            disabled={currentSlideIndex === presentation.slides.length - 1}
            className="bg-sky-600 hover:bg-sky-500 flex items-center space-x-2"
          >
            {!isArabicPresentation && (
              <span>{isArabicPresentation ? 'التالي' : 'Next'}</span>
            )}
            {!isArabicPresentation && (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            )}
            {isArabicPresentation && (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            )}
            {isArabicPresentation && <span>التالي</span>}
          </Button>
        </div>
      </div>
    </div>
  );
};
