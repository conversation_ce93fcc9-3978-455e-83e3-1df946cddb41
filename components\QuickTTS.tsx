import React, { useState, useEffect } from 'react';
import { Button } from './common/Button';

interface QuickTTSProps {
  text: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showLanguage?: boolean;
}

// Helper function to detect Arabic text
const isArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
  const totalChars = text.replace(/\s/g, '').length;
  return arabicChars ? (arabicChars.length / totalChars) > 0.3 : false;
};

// Get best voice for language
const getBestVoice = (language: string): SpeechSynthesisVoice | null => {
  const voices = speechSynthesis.getVoices();
  
  if (language === 'ar') {
    // Prefer Arabic voices
    const arabicVoices = ['ar-SA', 'ar-EG', 'ar-AE', 'ar-JO', 'ar-LB'];
    for (const voiceLang of arabicVoices) {
      const voice = voices.find(v => v.lang === voiceLang);
      if (voice) return voice;
    }
    return voices.find(v => v.lang.startsWith('ar')) || null;
  } else {
    // Prefer English voices
    const englishVoices = ['en-US', 'en-GB', 'en-AU', 'en-CA'];
    for (const voiceLang of englishVoices) {
      const voice = voices.find(v => v.lang === voiceLang);
      if (voice) return voice;
    }
    return voices.find(v => v.lang.startsWith('en')) || null;
  }
};

export const QuickTTS: React.FC<QuickTTSProps> = ({
  text,
  className = '',
  size = 'md',
  showLanguage = true
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [detectedLanguage, setDetectedLanguage] = useState<'ar' | 'en'>('en');
  const [isSupported, setIsSupported] = useState(true);

  useEffect(() => {
    // Check if TTS is supported
    if (!('speechSynthesis' in window)) {
      setIsSupported(false);
      return;
    }

    // Detect language
    if (text) {
      const isArabic = isArabicText(text);
      setDetectedLanguage(isArabic ? 'ar' : 'en');
    }
  }, [text]);

  const speak = () => {
    if (!text.trim() || !isSupported) return;

    if (isPlaying) {
      // Stop current speech
      speechSynthesis.cancel();
      setIsPlaying(false);
      return;
    }

    // Create utterance
    const utterance = new SpeechSynthesisUtterance(text);
    
    // Set language and voice
    const bestVoice = getBestVoice(detectedLanguage);
    if (bestVoice) {
      utterance.voice = bestVoice;
    }
    
    utterance.lang = detectedLanguage === 'ar' ? 'ar-SA' : 'en-US';
    utterance.rate = 0.9; // Slightly slower for better comprehension
    utterance.pitch = 1;
    utterance.volume = 1;

    utterance.onstart = () => {
      setIsPlaying(true);
    };

    utterance.onend = () => {
      setIsPlaying(false);
    };

    utterance.onerror = () => {
      setIsPlaying(false);
    };

    speechSynthesis.speak(utterance);
  };

  if (!isSupported) {
    return (
      <div className={`text-xs text-slate-500 ${className}`}>
        {detectedLanguage === 'ar' ? 'القراءة الصوتية غير مدعومة' : 'TTS not supported'}
      </div>
    );
  }

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-2',
    lg: 'text-base px-4 py-2'
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  return (
    <div className={`flex items-center space-x-2 rtl:space-x-reverse ${className}`}>
      {showLanguage && (
        <span className="text-xs text-slate-400">
          {detectedLanguage === 'ar' ? '🇸🇦' : '🇺🇸'}
        </span>
      )}
      
      <Button
        onClick={speak}
        className={`${sizeClasses[size]} ${
          isPlaying 
            ? 'bg-red-600 hover:bg-red-500' 
            : 'bg-blue-600 hover:bg-blue-500'
        } flex items-center space-x-1 rtl:space-x-reverse`}
        disabled={!text.trim()}
        title={
          detectedLanguage === 'ar' 
            ? (isPlaying ? 'إيقاف القراءة' : 'قراءة النص')
            : (isPlaying ? 'Stop Reading' : 'Read Text')
        }
      >
        {isPlaying ? (
          <>
            <svg className={iconSizes[size]} fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {size !== 'sm' && (
              <span>{detectedLanguage === 'ar' ? 'إيقاف' : 'Stop'}</span>
            )}
          </>
        ) : (
          <>
            <svg className={iconSizes[size]} fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.824L4.5 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.5l3.883-3.824zM15 8.5a2.5 2.5 0 000 5 .5.5 0 01-.5-.5v-4a.5.5 0 01.5-.5z"/>
              <path d="M17.5 6.5a4.5 4.5 0 000 9 .5.5 0 01-.5-.5v-8a.5.5 0 01.5-.5z"/>
            </svg>
            {size !== 'sm' && (
              <span>{detectedLanguage === 'ar' ? 'قراءة' : 'Read'}</span>
            )}
          </>
        )}
      </Button>
    </div>
  );
};
