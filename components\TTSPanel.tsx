import React, { useState, useEffect } from 'react';
import { TTSReader } from './TTSReader';
import { QuickTTS } from './QuickTTS';
import { Button } from './common/Button';
import { ttsService } from '../services/TTSService';

interface TTSPanelProps {
  isOpen: boolean;
  onClose: () => void;
  text: string;
  title?: string;
}

export const TTSPanel: React.FC<TTSPanelProps> = ({
  isOpen,
  onClose,
  text,
  title = 'Text-to-Speech Reader'
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [voiceStats, setVoiceStats] = useState({ arabic: 0, english: 0, total: 0 });
  const [detectedLanguage, setDetectedLanguage] = useState<'ar' | 'en'>('en');

  useEffect(() => {
    if (isOpen && text) {
      // Detect language
      const language = ttsService.detectLanguage(text);
      setDetectedLanguage(language);

      // Get voice statistics
      const stats = ttsService.getVoiceStats();
      setVoiceStats(stats);
    }
  }, [isOpen, text]);

  useEffect(() => {
    // Set up TTS callbacks
    ttsService.setCallbacks({
      onStart: () => setIsPlaying(true),
      onEnd: () => setIsPlaying(false),
      onError: (error) => {
        setIsPlaying(false);
        console.error('TTS Error:', error);
      }
    });

    return () => {
      // Cleanup
      ttsService.stop();
    };
  }, []);

  const handleQuickRead = () => {
    if (isPlaying) {
      ttsService.stop();
    } else {
      ttsService.quickSpeak(text, 0.9);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="tts-panel-overlay">
      <div className="tts-panel card-enhanced">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-600">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.824L4.5 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.5l3.883-3.824zM15 8.5a2.5 2.5 0 000 5 .5.5 0 01-.5-.5v-4a.5.5 0 01.5-.5z"/>
                <path d="M17.5 6.5a4.5 4.5 0 000 9 .5.5 0 01-.5-.5v-8a.5.5 0 01.5-.5z"/>
              </svg>
            </div>
            <div>
              <h2 className="text-lg font-semibold gradient-text">
                {detectedLanguage === 'ar' ? 'قارئ النصوص الصوتي المتقدم' : 'Advanced Text-to-Speech Reader'}
              </h2>
              <p className="text-sm text-slate-400">
                {detectedLanguage === 'ar'
                  ? `تم اكتشاف ${detectedLanguage === 'ar' ? 'النص العربي' : 'النص الإنجليزي'} - ${text.length} حرف`
                  : `${detectedLanguage === 'ar' ? 'Arabic' : 'English'} text detected - ${text.length} characters`
                }
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {/* Quick Read Button */}
            <Button
              onClick={handleQuickRead}
              className={`${
                isPlaying
                  ? 'bg-red-600 hover:bg-red-500'
                  : 'btn-primary'
              } flex items-center space-x-2 rtl:space-x-reverse`}
            >
              {isPlaying ? (
                <>
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <span>{detectedLanguage === 'ar' ? 'إيقاف' : 'Stop'}</span>
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                  <span>{detectedLanguage === 'ar' ? 'قراءة سريعة' : 'Quick Read'}</span>
                </>
              )}
            </Button>

            {/* Close Button */}
            <Button
              onClick={onClose}
              className="btn-secondary p-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </Button>
          </div>
        </div>

        {/* Voice Statistics */}
        <div className="p-4 glass-effect border-b border-slate-600">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="card-enhanced p-4">
              <div className="text-2xl font-bold text-green-400">{voiceStats.arabic}</div>
              <div className="text-sm text-slate-400">
                {detectedLanguage === 'ar' ? 'أصوات عربية' : 'Arabic Voices'}
              </div>
            </div>
            <div className="card-enhanced p-4">
              <div className="text-2xl font-bold text-blue-400">{voiceStats.english}</div>
              <div className="text-sm text-slate-400">
                {detectedLanguage === 'ar' ? 'أصوات إنجليزية' : 'English Voices'}
              </div>
            </div>
            <div className="card-enhanced p-4">
              <div className="text-2xl font-bold text-purple-400">{voiceStats.total}</div>
              <div className="text-sm text-slate-400">
                {detectedLanguage === 'ar' ? 'إجمالي الأصوات' : 'Total Voices'}
              </div>
            </div>
          </div>
        </div>

        {/* TTS Reader Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <TTSReader
            text={text}
            autoDetectLanguage={true}
            showControls={true}
            onStart={() => setIsPlaying(true)}
            onEnd={() => setIsPlaying(false)}
            onError={(error) => {
              setIsPlaying(false);
              console.error('TTS Panel Error:', error);
            }}
          />
        </div>

        {/* Footer */}
        <div className="p-4 bg-slate-700/30 border-t border-slate-600">
          <div className="flex items-center justify-between text-sm text-slate-400">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <span className="w-2 h-2 bg-green-400 rounded-full"></span>
              <span>
                {detectedLanguage === 'ar'
                  ? 'دعم كامل للغة العربية والإنجليزية'
                  : 'Full Arabic and English language support'
                }
              </span>
            </div>
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <span>
                {detectedLanguage === 'ar' ? 'الإصدار 1.2.1' : 'Version 1.2.1'}
              </span>
              <span>
                {detectedLanguage === 'ar' ? 'TTS متقدم' : 'Advanced TTS'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
