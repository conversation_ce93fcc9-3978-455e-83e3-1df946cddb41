import React, { useState, useEffect, useRef } from 'react';
import { Button } from './common/Button';

interface TTSReaderProps {
  text: string;
  className?: string;
  autoDetectLanguage?: boolean;
  showControls?: boolean;
  onStart?: () => void;
  onEnd?: () => void;
  onError?: (error: string) => void;
}

// Helper function to detect Arabic text
const isArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
  const totalChars = text.replace(/\s/g, '').length;
  return arabicChars ? (arabicChars.length / totalChars) > 0.3 : false;
};

// Get available voices for language
const getVoicesForLanguage = (language: string): SpeechSynthesisVoice[] => {
  const voices = speechSynthesis.getVoices();
  return voices.filter(voice => voice.lang.startsWith(language));
};

// Get best voice for language
const getBestVoice = (language: string): SpeechSynthesisVoice | null => {
  const voices = getVoicesForLanguage(language);

  if (language === 'ar') {
    // Prefer Arabic voices in this order
    const preferredArabicVoices = ['ar-SA', 'ar-EG', 'ar-AE', 'ar-JO', 'ar-LB', 'ar-MA', 'ar-TN', 'ar-DZ'];
    for (const voiceLang of preferredArabicVoices) {
      const voice = voices.find(v => v.lang === voiceLang);
      if (voice) return voice;
    }
    return voices.find(v => v.lang.startsWith('ar')) || null;
  } else {
    // Prefer English voices in this order
    const preferredEnglishVoices = ['en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN'];
    for (const voiceLang of preferredEnglishVoices) {
      const voice = voices.find(v => v.lang === voiceLang);
      if (voice) return voice;
    }
    return voices.find(v => v.lang.startsWith('en')) || null;
  }
};

export const TTSReader: React.FC<TTSReaderProps> = ({
  text,
  className = '',
  autoDetectLanguage = true,
  showControls = true,
  onStart,
  onEnd,
  onError
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentPosition, setCurrentPosition] = useState(0);
  const [duration, setDuration] = useState(0);
  const [rate, setRate] = useState(1);
  const [pitch, setPitch] = useState(1);
  const [volume, setVolume] = useState(1);
  const [selectedVoice, setSelectedVoice] = useState<SpeechSynthesisVoice | null>(null);
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [detectedLanguage, setDetectedLanguage] = useState<'ar' | 'en'>('en');
  const [highlightedText, setHighlightedText] = useState('');

  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);
  const textRef = useRef<HTMLDivElement>(null);

  // Initialize voices and detect language
  useEffect(() => {
    const loadVoices = () => {
      const voices = speechSynthesis.getVoices();
      setAvailableVoices(voices);

      if (autoDetectLanguage && text) {
        const isArabic = isArabicText(text);
        const language = isArabic ? 'ar' : 'en';
        setDetectedLanguage(language);

        const bestVoice = getBestVoice(language);
        setSelectedVoice(bestVoice);
      }
    };

    loadVoices();
    speechSynthesis.addEventListener('voiceschanged', loadVoices);

    return () => {
      speechSynthesis.removeEventListener('voiceschanged', loadVoices);
    };
  }, [text, autoDetectLanguage]);

  // Create speech utterance
  const createUtterance = (textToSpeak: string): SpeechSynthesisUtterance => {
    const utterance = new SpeechSynthesisUtterance(textToSpeak);

    if (selectedVoice) {
      utterance.voice = selectedVoice;
    }

    utterance.rate = rate;
    utterance.pitch = pitch;
    utterance.volume = volume;
    utterance.lang = detectedLanguage === 'ar' ? 'ar-SA' : 'en-US';

    utterance.onstart = () => {
      setIsPlaying(true);
      setIsPaused(false);
      onStart?.();
    };

    utterance.onend = () => {
      setIsPlaying(false);
      setIsPaused(false);
      setCurrentPosition(0);
      setHighlightedText('');
      onEnd?.();
    };

    utterance.onerror = (event) => {
      setIsPlaying(false);
      setIsPaused(false);
      const errorMessage = `خطأ في القراءة الصوتية: ${event.error}`;
      onError?.(errorMessage);
      console.error('TTS Error:', event.error);
    };

    utterance.onboundary = (event) => {
      if (event.name === 'word') {
        setCurrentPosition(event.charIndex);
        // Highlight current word
        const words = text.split(' ');
        let charCount = 0;
        for (let i = 0; i < words.length; i++) {
          if (charCount <= event.charIndex && event.charIndex < charCount + words[i].length) {
            setHighlightedText(words[i]);
            break;
          }
          charCount += words[i].length + 1; // +1 for space
        }
      }
    };

    return utterance;
  };

  // Play/Resume speech
  const play = () => {
    if (!text.trim()) return;

    if (isPaused) {
      speechSynthesis.resume();
      setIsPaused(false);
      setIsPlaying(true);
    } else {
      // Stop any current speech
      speechSynthesis.cancel();

      const utterance = createUtterance(text);
      utteranceRef.current = utterance;

      speechSynthesis.speak(utterance);
    }
  };

  // Pause speech
  const pause = () => {
    if (isPlaying) {
      speechSynthesis.pause();
      setIsPaused(true);
      setIsPlaying(false);
    }
  };

  // Stop speech
  const stop = () => {
    speechSynthesis.cancel();
    setIsPlaying(false);
    setIsPaused(false);
    setCurrentPosition(0);
    setHighlightedText('');
  };

  // Change voice
  const handleVoiceChange = (voiceURI: string) => {
    const voice = availableVoices.find(v => v.voiceURI === voiceURI);
    setSelectedVoice(voice || null);
  };

  // Get voices for current language
  const getCurrentLanguageVoices = () => {
    return availableVoices.filter(voice =>
      voice.lang.startsWith(detectedLanguage)
    );
  };

  return (
    <div className={`tts-reader ${className}`}>
      {/* Language Detection Display */}
      <div className="mb-4 p-3 bg-slate-700 rounded-lg border border-slate-600">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-slate-300">
            {detectedLanguage === 'ar' ? '🇸🇦 تم اكتشاف النص العربي' : '🇺🇸 English Text Detected'}
          </span>
          <span className="text-xs text-slate-400">
            {detectedLanguage === 'ar' ? 'سيتم استخدام صوت عربي' : 'Arabic/English voice will be used'}
          </span>
        </div>

        {/* Text Preview with Highlighting */}
        <div
          ref={textRef}
          className={`text-sm text-slate-200 bg-slate-800 p-2 rounded max-h-20 overflow-y-auto ${
            detectedLanguage === 'ar' ? 'text-right' : 'text-left'
          }`}
          dir={detectedLanguage === 'ar' ? 'rtl' : 'ltr'}
        >
          {text.split(' ').map((word, index) => (
            <span
              key={index}
              className={`${
                word === highlightedText
                  ? 'bg-yellow-400 text-slate-900 px-1 rounded'
                  : ''
              }`}
            >
              {word}{' '}
            </span>
          ))}
        </div>
      </div>

      {showControls && (
        <div className="space-y-4">
          {/* Main Controls */}
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {!isPlaying && !isPaused ? (
              <Button
                onClick={play}
                className="bg-green-600 hover:bg-green-500 flex items-center space-x-2 rtl:space-x-reverse"
                disabled={!text.trim()}
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
                <span>{detectedLanguage === 'ar' ? 'تشغيل' : 'Play'}</span>
              </Button>
            ) : (
              <Button
                onClick={isPlaying ? pause : play}
                className={`${
                  isPlaying
                    ? 'bg-yellow-600 hover:bg-yellow-500'
                    : 'bg-green-600 hover:bg-green-500'
                } flex items-center space-x-2 rtl:space-x-reverse`}
              >
                {isPlaying ? (
                  <>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <span>{detectedLanguage === 'ar' ? 'إيقاف مؤقت' : 'Pause'}</span>
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                    <span>{detectedLanguage === 'ar' ? 'متابعة' : 'Resume'}</span>
                  </>
                )}
              </Button>
            )}

            <Button
              onClick={stop}
              className="bg-red-600 hover:bg-red-500 flex items-center space-x-2 rtl:space-x-reverse"
              disabled={!isPlaying && !isPaused}
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
              </svg>
              <span>{detectedLanguage === 'ar' ? 'إيقاف' : 'Stop'}</span>
            </Button>
          </div>

          {/* Voice Selection */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-slate-300">
              {detectedLanguage === 'ar' ? 'اختيار الصوت:' : 'Select Voice:'}
            </label>
            <select
              value={selectedVoice?.voiceURI || ''}
              onChange={(e) => handleVoiceChange(e.target.value)}
              className="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-sky-500"
              title={detectedLanguage === 'ar' ? 'اختيار الصوت' : 'Select Voice'}
              aria-label={detectedLanguage === 'ar' ? 'اختيار الصوت' : 'Select Voice'}
            >
              <option value="">
                {detectedLanguage === 'ar' ? 'اختر صوت...' : 'Select a voice...'}
              </option>
              {getCurrentLanguageVoices().map((voice) => (
                <option key={voice.voiceURI} value={voice.voiceURI}>
                  {voice.name} ({voice.lang}) {voice.default ? '(Default)' : ''}
                </option>
              ))}
            </select>
          </div>

          {/* Speed Control */}
          <div className="space-y-2">
            <label htmlFor="speed-control" className="block text-sm font-medium text-slate-300">
              {detectedLanguage === 'ar' ? `سرعة القراءة: ${rate}x` : `Reading Speed: ${rate}x`}
            </label>
            <input
              id="speed-control"
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={rate}
              onChange={(e) => setRate(parseFloat(e.target.value))}
              className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer tts-slider"
              title={detectedLanguage === 'ar' ? 'سرعة القراءة' : 'Reading Speed'}
              aria-label={detectedLanguage === 'ar' ? 'سرعة القراءة' : 'Reading Speed'}
            />
          </div>

          {/* Pitch Control */}
          <div className="space-y-2">
            <label htmlFor="pitch-control" className="block text-sm font-medium text-slate-300">
              {detectedLanguage === 'ar' ? `نبرة الصوت: ${pitch}` : `Voice Pitch: ${pitch}`}
            </label>
            <input
              id="pitch-control"
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={pitch}
              onChange={(e) => setPitch(parseFloat(e.target.value))}
              className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer tts-slider"
              title={detectedLanguage === 'ar' ? 'نبرة الصوت' : 'Voice Pitch'}
              aria-label={detectedLanguage === 'ar' ? 'نبرة الصوت' : 'Voice Pitch'}
            />
          </div>

          {/* Volume Control */}
          <div className="space-y-2">
            <label htmlFor="volume-control" className="block text-sm font-medium text-slate-300">
              {detectedLanguage === 'ar' ? `مستوى الصوت: ${Math.round(volume * 100)}%` : `Volume: ${Math.round(volume * 100)}%`}
            </label>
            <input
              id="volume-control"
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={(e) => setVolume(parseFloat(e.target.value))}
              className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer tts-slider"
              title={detectedLanguage === 'ar' ? 'مستوى الصوت' : 'Volume'}
              aria-label={detectedLanguage === 'ar' ? 'مستوى الصوت' : 'Volume'}
            />
          </div>
        </div>
      )}
    </div>
  );
};
