
import React from 'react';
import type { Topic } from '../types';
import { Card } from './common/Card';
import { Button } from './common/Button';

interface TopicListProps {
  topics: Topic[];
  onSelectTopic: (topic: Topic) => void;
}

// Helper function to detect if text is primarily Arabic
const isArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
  const totalChars = text.replace(/\s/g, '').length;
  return arabicChars ? (arabicChars.length / totalChars) > 0.3 : false;
};

export const TopicList: React.FC<TopicListProps> = ({ topics, onSelectTopic }) => {
  if (!topics || topics.length === 0) {
    return <p className="text-slate-400 italic font-arabic">لم يتم استخراج مواضيع بعد، أو التحليل قيد التقدم.</p>;
  }

  // Detect if topics are in Arabic
  const hasArabicTopics = topics.some(topic => isArabicText(topic.title + " " + topic.summary));

  return (
    <div className="space-y-4" dir={hasArabicTopics ? "rtl" : "ltr"}>
      <h3 className={`text-xl font-semibold text-sky-300 border-b border-sky-700 pb-2 ${hasArabicTopics ? 'font-arabic' : ''}`}>
        {hasArabicTopics ? 'المواضيع الرئيسية' : 'Key Topics'}
      </h3>
      <div className="max-h-96 overflow-y-auto space-y-3 pr-2">
        {topics.map((topic) => (
          <Card key={topic.id} className="bg-slate-700 hover:bg-slate-600 transition-colors">
            <h4 className={`text-lg font-semibold text-sky-400 ${hasArabicTopics ? 'font-arabic' : ''}`}>
              {topic.title}
            </h4>
            <p className={`text-sm text-slate-300 mt-1 mb-3 leading-relaxed ${hasArabicTopics ? 'font-arabic' : ''}`}>
              {topic.summary}
            </p>
            <Button
              onClick={() => onSelectTopic(topic)}
              className={`w-full text-sm bg-cyan-600 hover:bg-cyan-500 ${hasArabicTopics ? 'font-arabic' : ''}`}
            >
              {hasArabicTopics ? 'إنشاء عرض تقديمي حول هذا الموضوع' : 'Generate Presentation on this Topic'}
            </Button>
          </Card>
        ))}
      </div>
    </div>
  );
};
