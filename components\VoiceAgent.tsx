import React, { useState, useCallback, useEffect, useRef } from 'react';
import { GoogleGenAI, <PERSON><PERSON>, GenerateContentResponse } from '@google/genai';
import { Message, SpeechRecognition, SpeechRecognitionEvent, SpeechRecognitionErrorEvent } from '../types';
import { GEMINI_TEXT_MODEL } from '../constants';
import { Button } from './common/Button'; // Assuming Button component exists

interface VoiceAgentProps {
  selectedText: string;
  aiInstance: GoogleGenAI | null;
}

// Helper function to detect if text is primarily Arabic
const isArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
  const totalChars = text.replace(/\s/g, '').length;
  return arabicChars ? (arabicChars.length / totalChars) > 0.3 : false;
};

// Simple SVG Icons
const MicIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={`w-5 h-5 ${className}`}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15c.621 0 1.19-.036 1.75-.108M12 15c-.621 0-1.19-.036-1.75-.108m0 0A12.001 12.001 0 0 1 5.25 6.038M12 15c.621 0 1.19.036 1.75-.108m0 0A12.001 12.001 0 0 0 18.75 6.038M5.25 6.038A11.953 11.953 0 0 0 12 3c1.352 0 2.646.214 3.86.61M5.25 6.038V4.5M18.75 6.038V4.5" />
  </svg>
);

const SpeakIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={`w-5 h-5 ${className}`}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.114 5.636a9 9 0 0 1 0 12.728M16.463 8.288a5.25 5.25 0 0 1 0 7.424M6.75 8.25l4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.01 9.01 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z" />
  </svg>
);


export const VoiceAgent: React.FC<VoiceAgentProps> = ({ selectedText, aiInstance }) => {
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [chatMessages, setChatMessages] = useState<Message[]>([]);
  const [userInput, setUserInput] = useState('');
  const [isLoadingAI, setIsLoadingAI] = useState(false);
  const speechRecognitionRef = useRef<SpeechRecognition | null>(null);
  const chatSessionRef = useRef<Chat | null>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Detect if selected text is Arabic
  const isArabic = isArabicText(selectedText);

  const handleSendChatMessageLogic = useCallback(async (messageText?: string) => {
    const textToSend = messageText || userInput;
    if (!textToSend.trim() || !chatSessionRef.current || isLoadingAI) {
      return;
    }

    const newUserMessage: Message = { id: Date.now().toString(), text: textToSend, sender: 'user', timestamp: new Date() };
    setChatMessages(prev => [...prev, newUserMessage]);

    if (!messageText) { // Only clear userInput if message came from manual input
      setUserInput('');
    }

    setIsLoadingAI(true);

    try {
      const response: GenerateContentResponse = await chatSessionRef.current.sendMessage({ message: textToSend });
      const aiResponseText = response.text;
      const newAiMessage: Message = { id: (Date.now() + 1).toString(), text: aiResponseText, sender: 'ai', timestamp: new Date() };
      setChatMessages(prev => [...prev, newAiMessage]);
    } catch (error) {
      console.error("Error sending chat message to AI:", error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: isArabic ? "عذراً، لم أتمكن من الحصول على رد. يرجى المحاولة مرة أخرى." : "Sorry, I couldn't get a response. Please try again.",
        sender: 'ai',
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoadingAI(false);
    }
  }, [userInput, isLoadingAI, chatSessionRef]); // chatSessionRef is stable, .current is used

  const handleSendChatMessageLogicRef = useRef(handleSendChatMessageLogic);
  useEffect(() => {
    handleSendChatMessageLogicRef.current = handleSendChatMessageLogic;
  }, [handleSendChatMessageLogic]);

  // Initialize SpeechRecognition
  useEffect(() => {
    const SpeechRecognitionAPI = window.SpeechRecognition || window.webkitSpeechRecognition;

    if (SpeechRecognitionAPI) {
      const recognition = new SpeechRecognitionAPI();
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = isArabic ? 'ar-SA' : 'en-US';

      recognition.onresult = (event: SpeechRecognitionEvent) => {
        const transcript = event.results[event.results.length - 1][0].transcript.trim();
        handleSendChatMessageLogicRef.current(transcript);
      };
      recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
      };
      recognition.onend = () => {
        setIsListening(false);
      };
      speechRecognitionRef.current = recognition;

      return () => {
        if (speechRecognitionRef.current) {
          speechRecognitionRef.current.abort();
        }
      };
    } else {
      console.warn('Speech Recognition API not supported in this browser.');
    }
  }, []); // Empty dependency array: runs once on mount and cleanup on unmount.

  // Initialize Gemini Chat session when selectedText or aiInstance changes
  useEffect(() => {
    if (aiInstance && selectedText) {
      const systemInstruction = isArabic
        ? `أنت مساعد ذكي. لقد اختار المستخدم النص التالي من كتاب: "${selectedText}". ساعدهم على فهم أو مناقشة هذا النص. اجعل ردودك مختصرة وذات صلة بالنص المقدم. يرجى الرد باللغة العربية الفصحى الصحيحة.`
        : `You are an AI assistant. The user has selected the following text from a book: "${selectedText}". Help them understand or discuss this text. Keep your responses concise and relevant to the provided text snippet.`;

      chatSessionRef.current = aiInstance.chats.create({
        model: GEMINI_TEXT_MODEL,
        config: {
          systemInstruction,
        },
      });
      setChatMessages([]); // Reset chat when selected text changes
      setUserInput(''); // Also clear user input field
    }
  }, [selectedText, aiInstance, isArabic]);

  // Scroll to bottom of chat messages
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatMessages]);


  const handleSpeakSelectedText = useCallback(() => {
    if (isSpeaking || !selectedText) return;
    setIsSpeaking(true);
    const utterance = new SpeechSynthesisUtterance(selectedText);

    // Set language and voice for Arabic text
    if (isArabic) {
      utterance.lang = 'ar-SA';
      utterance.rate = 0.8; // Slower rate for Arabic
      utterance.pitch = 1.0;
    } else {
      utterance.lang = 'en-US';
      utterance.rate = 1.0;
      utterance.pitch = 1.0;
    }

    utterance.onend = () => setIsSpeaking(false);
    utterance.onerror = (e) => {
      console.error("Speech synthesis error:", e);
      setIsSpeaking(false);
    };
    speechSynthesis.speak(utterance);
  }, [selectedText, isSpeaking, isArabic]);

  const handleToggleListen = () => {
    if (!speechRecognitionRef.current) {
        alert(isArabic ? "التعرف على الصوت غير مدعوم أو غير مفعل في متصفحك." : "Speech recognition is not supported or enabled in your browser.");
        return;
    }
    if (isListening) {
      speechRecognitionRef.current.stop();
      // setIsListening(false) will be called by onend
    } else {
      try {
        speechRecognitionRef.current.start();
        setIsListening(true);
      } catch (e) {
        console.error("Error starting speech recognition:", e);
        setIsListening(false);
        alert(isArabic ? `لا يمكن بدء التعرف على الصوت: ${(e as Error).message}` : `Could not start voice recognition: ${(e as Error).message}`);
      }
    }
  };

  const UIsendMessage = () => {
    handleSendChatMessageLogic(); // Calls the memoized logic which uses current userInput
  };


  return (
    <div className="p-4 bg-slate-700 rounded-lg shadow-inner space-y-3" dir={isArabic ? "rtl" : "ltr"}>
      <Button
        onClick={handleSpeakSelectedText}
        disabled={isSpeaking || !selectedText}
        className={`w-full bg-teal-600 hover:bg-teal-500 flex items-center justify-center ${isArabic ? 'font-arabic' : ''}`}
      >
        <SpeakIcon className={isArabic ? "ml-2" : "mr-2"}/>
        {isArabic ? "قراءة النص المحدد بصوت عالٍ" : "Read Selected Aloud"}
      </Button>

      <div className="border-t border-slate-600 pt-3">
        <h4 className={`text-md font-semibold text-sky-300 mb-2 ${isArabic ? 'font-arabic' : ''}`}>
          {isArabic ? "محادثة حول هذا النص المحدد:" : "Chat about this selection:"}
        </h4>
        <div ref={chatContainerRef} className="h-48 overflow-y-auto bg-slate-600 p-3 rounded-md mb-2 space-y-2 text-sm">
          {chatMessages.length === 0 && (
            <p className={`text-slate-400 italic ${isArabic ? 'font-arabic' : ''}`}>
              {isArabic ? "اسأل سؤالاً حول النص المحدد..." : "Ask a question about the selected text..."}
            </p>
          )}
          {chatMessages.map(msg => (
            <div key={msg.id} className={`flex ${msg.sender === 'user' ? (isArabic ? 'justify-start' : 'justify-end') : (isArabic ? 'justify-end' : 'justify-start')}`}>
              <div className={`max-w-[80%] p-2 rounded-lg leading-relaxed ${isArabic ? 'font-arabic' : ''} ${msg.sender === 'user' ? 'bg-sky-600 text-white' : 'bg-slate-500 text-slate-100'}`}>
                {msg.text}
              </div>
            </div>
          ))}
           {isLoadingAI && (
             <div className={`text-center text-slate-400 italic ${isArabic ? 'font-arabic' : ''}`}>
               {isArabic ? "الذكاء الاصطناعي يفكر..." : "AI is thinking..."}
             </div>
           )}
        </div>
        <div className={`flex items-center ${isArabic ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
          <input
            type="text"
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && !isLoadingAI && userInput.trim() && UIsendMessage()}
            placeholder={isArabic ? "اسأل سؤالاً..." : "Ask a question..."}
            className={`flex-grow p-2 rounded-md bg-slate-500 text-slate-100 border border-slate-400 focus:ring-sky-500 focus:border-sky-500 ${isArabic ? 'font-arabic text-right' : 'text-left'}`}
            disabled={isLoadingAI}
            dir={isArabic ? "rtl" : "ltr"}
          />
          <Button
            onClick={handleToggleListen}
            className={`p-2 ${isListening ? 'bg-red-500 hover:bg-red-400' : 'bg-sky-600 hover:bg-sky-500'}`}
            disabled={isLoadingAI}
            title={isArabic ? (isListening ? "إيقاف التسجيل" : "بدء التسجيل الصوتي") : (isListening ? "Stop Recording" : "Start Voice Recording")}
          >
            <MicIcon />
          </Button>
          <Button
            onClick={UIsendMessage}
            disabled={isLoadingAI || !userInput.trim()}
            className={`bg-emerald-600 hover:bg-emerald-500 ${isArabic ? 'font-arabic' : ''}`}
          >
            {isArabic ? "إرسال" : "Send"}
          </Button>
        </div>
      </div>
    </div>
  );
};
