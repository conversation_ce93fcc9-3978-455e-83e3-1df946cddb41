
<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">

  <!-- Primary Meta Tags -->
  <title>AI Interactive Book Chat - Transform Your Reading Experience | Last Updated January 2025</title>
  <meta name="title" content="AI Interactive Book Chat - Transform Your Reading Experience">
  <meta name="description" content="Revolutionary AI-powered book analysis platform. Upload PDF, DOCX, or TXT files and transform your reading with intelligent topic extraction, interactive presentations, voice chat, and visual diagrams. Last updated January 2025.">
  <meta name="keywords" content="AI book analysis, interactive reading, education technology, voice chat, presentations, PDF reader, document analysis, Gemini AI, React, TypeScript, 2025">
  <meta name="author" content="AI Interactive Book Chat Team">
  <meta name="robots" content="index, follow">
  <meta name="language" content="English">
  <meta name="revisit-after" content="7 days">
  <meta name="distribution" content="global">
  <meta name="rating" content="general">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://ai-book-chat.app/">
  <meta property="og:title" content="AI Interactive Book Chat - Transform Your Reading Experience">
  <meta property="og:description" content="Revolutionary AI-powered book analysis platform. Upload books and transform your reading with intelligent analysis, interactive presentations, and voice chat features.">
  <meta property="og:image" content="https://ai-book-chat.app/og-image.png">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:site_name" content="AI Interactive Book Chat">
  <meta property="og:locale" content="en_US">
  <meta property="og:updated_time" content="2025-01-01T00:00:00Z">

  <!-- Twitter -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:url" content="https://ai-book-chat.app/">
  <meta name="twitter:title" content="AI Interactive Book Chat - Transform Your Reading Experience">
  <meta name="twitter:description" content="Revolutionary AI-powered book analysis platform. Upload books and transform your reading with intelligent analysis, interactive presentations, and voice chat features.">
  <meta name="twitter:image" content="https://ai-book-chat.app/twitter-image.png">
  <meta name="twitter:creator" content="@aibookchat">
  <meta name="twitter:site" content="@aibookchat">

  <!-- PWA Meta Tags -->
  <meta name="application-name" content="AI Book Chat">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="AI Book Chat">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="msapplication-TileColor" content="#0ea5e9">
  <meta name="msapplication-config" content="/browserconfig.xml">
  <meta name="theme-color" content="#0ea5e9" media="(prefers-color-scheme: light)">
  <meta name="theme-color" content="#0f172a" media="(prefers-color-scheme: dark)">

  <!-- Favicon and Icons -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="manifest" href="/manifest.json">
  <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#0ea5e9">

  <!-- Preconnect to external domains for performance -->
  <link rel="preconnect" href="https://cdn.tailwindcss.com" crossorigin>
  <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
  <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
  <link rel="preconnect" href="https://esm.sh" crossorigin>
  <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- DNS Prefetch for additional performance -->
  <link rel="dns-prefetch" href="https://generativelanguage.googleapis.com">
  <link rel="dns-prefetch" href="https://ai.google.dev">

  <!-- Canonical URL -->
  <link rel="canonical" href="https://ai-book-chat.app/">

  <!-- External Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

  <!-- External Scripts with performance optimizations -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // Tailwind CSS configuration with enhanced theme
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          fontFamily: {
            'sans': ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
            'mono': ['JetBrains Mono', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace']
          },
          animation: {
            'fade-in': 'fadeIn 0.6s ease-in-out',
            'slide-up': 'slideUp 0.6s ease-out',
            'slide-down': 'slideDown 0.6s ease-out',
            'slide-left': 'slideLeft 0.6s ease-out',
            'slide-right': 'slideRight 0.6s ease-out',
            'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            'bounce-gentle': 'bounceGentle 2s ease-in-out infinite',
            'glow': 'glow 2s ease-in-out infinite alternate',
            'float': 'float 3s ease-in-out infinite',
            'shimmer': 'shimmer 2s linear infinite',
          },
          keyframes: {
            fadeIn: {
              '0%': { opacity: '0', transform: 'translateY(10px)' },
              '100%': { opacity: '1', transform: 'translateY(0)' },
            },
            slideUp: {
              '0%': { transform: 'translateY(30px)', opacity: '0' },
              '100%': { transform: 'translateY(0)', opacity: '1' },
            },
            slideDown: {
              '0%': { transform: 'translateY(-30px)', opacity: '0' },
              '100%': { transform: 'translateY(0)', opacity: '1' },
            },
            slideLeft: {
              '0%': { transform: 'translateX(30px)', opacity: '0' },
              '100%': { transform: 'translateX(0)', opacity: '1' },
            },
            slideRight: {
              '0%': { transform: 'translateX(-30px)', opacity: '0' },
              '100%': { transform: 'translateX(0)', opacity: '1' },
            },
            bounceGentle: {
              '0%, 100%': { transform: 'translateY(0)' },
              '50%': { transform: 'translateY(-10px)' },
            },
            glow: {
              '0%': { boxShadow: '0 0 20px rgba(14, 165, 233, 0.3)' },
              '100%': { boxShadow: '0 0 30px rgba(14, 165, 233, 0.6)' },
            },
            float: {
              '0%, 100%': { transform: 'translateY(0px)' },
              '50%': { transform: 'translateY(-20px)' },
            },
            shimmer: {
              '0%': { backgroundPosition: '-200% 0' },
              '100%': { backgroundPosition: '200% 0' },
            }
          },
          colors: {
            primary: {
              50: '#f0f9ff',
              100: '#e0f2fe',
              200: '#bae6fd',
              300: '#7dd3fc',
              400: '#38bdf8',
              500: '#0ea5e9',
              600: '#0284c7',
              700: '#0369a1',
              800: '#075985',
              900: '#0c4a6e',
            }
          },
          backgroundImage: {
            'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
            'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
            'shimmer': 'linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent)',
          }
        }
      }
    }

    // PDF.js worker configuration
    window.pdfjsWorkerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

    // Performance monitoring
    window.performance.mark('html-start');
  </script>

  <!-- External Libraries with optimized loading -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js" defer></script>
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js" defer></script>
  <style>
    /* Enhanced CSS Styles - Last Updated January 2025 */

    /* Root CSS Variables for Enhanced Theming */
    :root {
      --primary-50: #f0f9ff;
      --primary-100: #e0f2fe;
      --primary-200: #bae6fd;
      --primary-300: #7dd3fc;
      --primary-400: #38bdf8;
      --primary-500: #0ea5e9;
      --primary-600: #0284c7;
      --primary-700: #0369a1;
      --primary-800: #075985;
      --primary-900: #0c4a6e;

      --background-primary: #0f172a;
      --background-secondary: #1e293b;
      --background-tertiary: #334155;
      --text-primary: #f1f5f9;
      --text-secondary: #cbd5e1;
      --text-muted: #94a3b8;
      --border-color: #475569;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --error-color: #ef4444;
      --shadow-color: rgba(0, 0, 0, 0.25);

      --border-radius: 12px;
      --border-radius-lg: 16px;
      --transition-fast: 0.15s ease;
      --transition-normal: 0.3s ease;
      --transition-slow: 0.6s ease;
    }

    /* Global Enhanced Styles */
    * {
      box-sizing: border-box;
    }

    html {
      scroll-behavior: smooth;
      font-size: 16px;
      line-height: 1.6;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
      overflow-x: hidden;
    }

    /* Enhanced Loading Screen */
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 50%, var(--primary-900) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(10px);
    }

    .loading-screen.fade-out {
      opacity: 0;
      pointer-events: none;
    }

    .loading-content {
      text-align: center;
      animation: float 3s ease-in-out infinite;
    }

    .loading-spinner {
      width: 80px;
      height: 80px;
      margin: 0 auto 24px;
      border: 4px solid var(--primary-200);
      border-top: 4px solid var(--primary-500);
      border-radius: 50%;
      animation: spin 1s linear infinite, glow 2s ease-in-out infinite alternate;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
    }

    @keyframes glow {
      0% { box-shadow: 0 0 20px rgba(14, 165, 233, 0.3); }
      100% { box-shadow: 0 0 30px rgba(14, 165, 233, 0.6); }
    }

    /* Enhanced Mermaid Diagram Styles */
    .mermaid {
      background: transparent;
      border-radius: var(--border-radius);
      padding: 16px;
      margin: 16px 0;
      transition: all var(--transition-normal);
    }

    .mermaid svg {
      max-width: 100%;
      height: auto;
      background: transparent;
      border-radius: var(--border-radius);
      filter: drop-shadow(0 4px 6px var(--shadow-color));
    }

    .mermaid .node rect,
    .mermaid .node circle,
    .mermaid .node polygon {
      fill: var(--background-secondary);
      stroke: var(--primary-500);
      stroke-width: 2px;
      filter: drop-shadow(0 2px 4px var(--shadow-color));
    }

    .mermaid .edgePath .path {
      stroke: var(--primary-400);
      stroke-width: 2px;
    }

    .mermaid .edgeLabel {
      background-color: var(--background-secondary);
      color: var(--text-primary);
      border-radius: 4px;
      padding: 4px 8px;
    }

    /* Enhanced Page Transition Styles for BookViewer */
    .page-container {
      perspective: 2000px;
      perspective-origin: center center;
    }

    .page-content {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
      transform-style: preserve-3d;
      backface-visibility: hidden;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: 0 10px 25px var(--shadow-color);
    }

    .page-enter {
      transform: rotateY(0deg) translateZ(0);
      opacity: 1;
    }

    .page-exit-left {
      transform-origin: left center;
      transform: rotateY(-90deg) translateZ(-100px);
      opacity: 0.7;
    }

    .page-enter-from-right {
      transform-origin: left center;
      transform: rotateY(90deg) translateZ(-100px);
      opacity: 0.7;
    }

    .page-exit-right {
      transform-origin: right center;
      transform: rotateY(90deg) translateZ(-100px);
      opacity: 0.7;
    }

    .page-enter-from-left {
      transform-origin: right center;
      transform: rotateY(-90deg) translateZ(-100px);
      opacity: 0.7;
    }

    /* Enhanced Scrollbar Styling */
    ::-webkit-scrollbar {
      width: 12px;
      height: 12px;
    }

    ::-webkit-scrollbar-track {
      background: var(--background-secondary);
      border-radius: 6px;
      margin: 2px;
    }

    ::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, var(--primary-600), var(--primary-500));
      border-radius: 6px;
      border: 2px solid var(--background-secondary);
      transition: all var(--transition-normal);
    }

    ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(135deg, var(--primary-500), var(--primary-400));
      transform: scale(1.1);
    }

    /* Enhanced Focus Styles for Accessibility */
    .focus-visible:focus,
    button:focus-visible,
    input:focus-visible,
    textarea:focus-visible,
    select:focus-visible {
      outline: 3px solid var(--primary-400);
      outline-offset: 2px;
      border-radius: var(--border-radius);
    }

    /* Enhanced Button Hover Effects */
    .btn-enhanced {
      position: relative;
      overflow: hidden;
      transition: all var(--transition-normal);
      transform: translateY(0);
    }

    .btn-enhanced::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.6s ease;
    }

    .btn-enhanced:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px var(--shadow-color);
    }

    .btn-enhanced:hover::before {
      left: 100%;
    }

    /* Enhanced Text Selection */
    ::selection {
      background: var(--primary-500);
      color: white;
      text-shadow: none;
    }

    ::-moz-selection {
      background: var(--primary-500);
      color: white;
      text-shadow: none;
    }

    /* Enhanced Typography */
    .text-gradient {
      background: linear-gradient(135deg, var(--primary-400), var(--primary-300), #06b6d4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      body {
        font-size: 14px;
      }

      .page-content {
        border-radius: 8px;
      }

      .loading-spinner {
        width: 60px;
        height: 60px;
      }
    }

    /* Print Styles */
    @media print {
      .no-print,
      .loading-screen,
      button {
        display: none !important;
      }

      body {
        background: white !important;
        color: black !important;
      }
    }

    /* Reduced Motion for Accessibility */
    @media (prefers-reduced-motion: reduce) {
      *,
      *::before,
      *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }
    }

    /* Custom Utility Classes */
    .glass-effect {
      background: rgba(30, 41, 59, 0.7);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .text-shadow-enhanced {
      text-shadow: 0 2px 4px var(--shadow-color);
    }

  </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.1.0"
  }
}
</script>
</head>
<body class="bg-slate-900 text-slate-100 dark font-sans antialiased">
  <!-- Enhanced Loading Screen - Updated January 2025 -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <h2 class="text-3xl font-bold text-gradient mb-2">AI Interactive Book Chat</h2>
      <p class="text-slate-400 text-lg mb-4">Transforming your reading experience...</p>
      <div class="flex items-center justify-center space-x-2">
        <div class="w-2 h-2 bg-primary-400 rounded-full animate-bounce" style="animation-delay: 0ms;"></div>
        <div class="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style="animation-delay: 150ms;"></div>
        <div class="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style="animation-delay: 300ms;"></div>
      </div>
      <p class="text-xs text-slate-500 mt-4">Last updated: January 2025</p>
    </div>
  </div>

  <!-- Main Application Container -->
  <div id="root" class="animate-fade-in"></div>

  <!-- Enhanced Scripts - Updated January 2025 -->
  <script>
    // Performance monitoring and analytics
    window.performance.mark('app-start');

    // Enhanced service worker registration with update handling
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', async () => {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js');
          console.log('✅ Service Worker registered successfully:', registration.scope);

          // Handle service worker updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // New content is available, show update notification
                console.log('🔄 New content available! Please refresh.');
                // You could show a toast notification here
              }
            });
          });
        } catch (error) {
          console.error('❌ Service Worker registration failed:', error);
        }
      });
    }

    // Enhanced loading screen management
    let loadingHidden = false;

    function hideLoadingScreen() {
      if (loadingHidden) return;
      loadingHidden = true;

      const loadingScreen = document.getElementById('loading-screen');
      if (loadingScreen) {
        // Add fade out animation
        loadingScreen.classList.add('fade-out');

        // Remove from DOM after animation
        setTimeout(() => {
          loadingScreen.style.display = 'none';
          loadingScreen.remove();
        }, 800);
      }

      // Mark performance milestone
      window.performance.mark('loading-screen-hidden');
    }

    // Hide loading screen when app is ready
    window.addEventListener('load', () => {
      // Ensure minimum loading time for smooth UX
      setTimeout(hideLoadingScreen, 1200);
    });

    // Also hide when React app mounts (fallback)
    window.addEventListener('app-mounted', hideLoadingScreen);

    // Enhanced error handling with user feedback
    window.addEventListener('error', (event) => {
      console.error('🚨 Global error:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      });

      // Hide loading screen on error
      hideLoadingScreen();

      // You could send this to an error reporting service
      // Example: Sentry.captureException(event.error);
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      console.error('🚨 Unhandled promise rejection:', event.reason);

      // Hide loading screen on error
      hideLoadingScreen();

      // You could send this to an error reporting service
      // Example: Sentry.captureException(event.reason);
    });

    // Performance monitoring
    window.addEventListener('load', () => {
      // Use requestIdleCallback for non-critical performance logging
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
          if ('performance' in window && 'getEntriesByType' in performance) {
            const perfData = performance.getEntriesByType('navigation')[0];
            const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
            const domContentLoaded = perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart;

            console.log('📊 Performance Metrics:', {
              'Page Load Time': `${loadTime.toFixed(2)}ms`,
              'DOM Content Loaded': `${domContentLoaded.toFixed(2)}ms`,
              'First Contentful Paint': performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 'N/A'
            });
          }
        });
      }
    });

    // Enhanced PWA install prompt handling
    let deferredPrompt;

    window.addEventListener('beforeinstallprompt', (e) => {
      console.log('💾 PWA install prompt available');
      e.preventDefault();
      deferredPrompt = e;

      // You could show a custom install button here
      // Example: showInstallButton();
    });

    window.addEventListener('appinstalled', () => {
      console.log('✅ PWA was installed successfully');
      deferredPrompt = null;

      // Track installation analytics
      // Example: gtag('event', 'pwa_install');
    });

    // Theme detection and handling
    function updateTheme() {
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      document.documentElement.classList.toggle('dark', isDark);

      // Update theme-color meta tag
      const themeColorMeta = document.querySelector('meta[name="theme-color"]');
      if (themeColorMeta) {
        themeColorMeta.content = isDark ? '#0f172a' : '#0ea5e9';
      }
    }

    // Listen for theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', updateTheme);
    updateTheme(); // Set initial theme

    // Keyboard shortcuts for accessibility
    document.addEventListener('keydown', (e) => {
      // Skip to main content (Alt + M)
      if (e.altKey && e.key === 'm') {
        e.preventDefault();
        const main = document.querySelector('main');
        if (main) {
          main.focus();
          main.scrollIntoView({ behavior: 'smooth' });
        }
      }

      // Toggle high contrast mode (Alt + H)
      if (e.altKey && e.key === 'h') {
        e.preventDefault();
        document.body.classList.toggle('high-contrast');
      }
    });

    // Initialize app metadata
    window.APP_INFO = {
      name: 'AI Interactive Book Chat',
      version: '1.0.0',
      lastUpdated: 'January 2025',
      buildDate: new Date().toISOString(),
      features: [
        'AI-powered book analysis',
        'Interactive presentations',
        'Voice chat integration',
        'Multi-format support',
        'Progressive Web App',
        'Offline functionality'
      ]
    };

    console.log('🚀 AI Interactive Book Chat initialized:', window.APP_INFO);
  </script>

  <!-- Main Application Script -->
  <script type="module" src="/index.tsx"></script>

  <!-- Structured Data for SEO -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "AI Interactive Book Chat",
    "description": "Revolutionary AI-powered book analysis platform. Upload PDF, DOCX, or TXT files and transform your reading with intelligent topic extraction, interactive presentations, voice chat, and visual diagrams.",
    "url": "https://ai-book-chat.app",
    "applicationCategory": "EducationalApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "author": {
      "@type": "Organization",
      "name": "AI Interactive Book Chat Team"
    },
    "dateModified": "2025-01-01",
    "version": "1.0.0",
    "browserRequirements": "Requires JavaScript. Modern browser recommended.",
    "softwareVersion": "1.0.0",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "150"
    },
    "featureList": [
      "AI-powered book analysis",
      "Multi-format file support (PDF, DOCX, TXT)",
      "Interactive topic extraction",
      "Dynamic presentation generation",
      "Voice chat integration",
      "Visual diagram creation",
      "Progressive Web App",
      "Offline functionality",
      "Responsive design",
      "Accessibility features"
    ]
  }
  </script>
</body>
</html>
