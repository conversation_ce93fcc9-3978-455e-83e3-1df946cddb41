
<!DOCTYPE html>
<html lang="ar" dir="rtl" class="scroll-smooth">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">

  <!-- Primary Meta Tags -->
  <title>🚀 محادثة الكتب التفاعلية بالذكاء الاصطناعي - مع مستكشف PDF Flash | الإصدار 1.2.0</title>
  <meta name="title" content="محادثة الكتب التفاعلية بالذكاء الاصطناعي - مع مستكشف PDF Flash">
  <meta name="description" content="🎯 منصة ثورية لتحليل الكتب وإنشاء عروض تقديمية بمحتوى حقيقي! ✨ مستكشف PDF Flash ✨ صور وفيديوهات تفاعلية ✨ إحصائيات حديثة ✨ مصادر موثوقة ✨ دعم عربي كامل.">

  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📚</text></svg>">

  <!-- External Fonts with Arabic Support -->
  <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Noto+Sans+Arabic:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

  <!-- External Scripts -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // Tailwind CSS configuration with RTL support
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          fontFamily: {
            'sans': ['Noto Sans Arabic', 'Inter', 'system-ui', 'sans-serif'],
            'arabic': ['Noto Sans Arabic', 'Tahoma', 'Arial', 'sans-serif'],
            'mono': ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace']
          }
        }
      }
    }

    // PDF.js worker configuration
    window.pdfjsWorkerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
  </script>

  <!-- External Libraries -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js" defer></script>
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js" defer></script>
  <style>
    /* Enhanced CSS Styles for AI Interactive Book Chat with PDF Flash Explorer - Version 1.2.0 */

    /* Root CSS Variables */
    :root {
      --primary-500: #0ea5e9;
      --primary-600: #0284c7;
      --background-primary: #0f172a;
      --background-secondary: #1e293b;
      --text-primary: #f1f5f9;
      --border-radius: 12px;
      --transition-normal: 0.3s ease;
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* Global Styles with RTL Support */
    * {
      box-sizing: border-box;
    }

    html {
      scroll-behavior: smooth;
      font-size: 16px;
      line-height: 1.6;
    }

    body {
      font-family: 'Noto Sans Arabic', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
      overflow-x: hidden;
      direction: rtl;
      background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 50%, #0c4a6e 100%);
      min-height: 100vh;
    }

    /* Enhanced Loading Screen */
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 50%, #0c4a6e 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }

    .loading-screen.fade-out {
      opacity: 0;
      pointer-events: none;
    }

    .loading-content {
      text-align: center;
      animation: float 3s ease-in-out infinite;
    }

    .loading-spinner {
      width: 80px;
      height: 80px;
      margin: 0 auto 24px;
      border: 4px solid #bae6fd;
      border-top: 4px solid var(--primary-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    /* Performance-optimized animations using will-change */
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes slideUp {
      from { opacity: 0; transform: translateY(30px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* Animation delay utilities */
    .bounce-delay-0 { animation-delay: 0ms; }
    .bounce-delay-150 { animation-delay: 150ms; }
    .bounce-delay-300 { animation-delay: 300ms; }

    /* Page animations */
    .animate-fade-in {
      animation: fadeIn 0.8s ease-in-out;
    }

    .animate-slide-up {
      animation: slideUp 0.6s ease-out;
    }

    /* Enhanced Mermaid Diagram Styles */
    .mermaid {
      background: transparent;
      border-radius: var(--border-radius);
      padding: 16px;
      margin: 16px 0;
      direction: ltr; /* Keep diagrams LTR */
      box-shadow: var(--shadow-xl);
      border: 1px solid rgba(148, 163, 184, 0.1);
    }

    .mermaid svg {
      max-width: 100%;
      height: auto;
      background: transparent;
      border-radius: var(--border-radius);
    }

    /* Enhanced Page Transition Styles with RTL Support */
    .page-content {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      transition: transform var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1),
                  opacity var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: var(--border-radius);
      overflow: hidden;
      will-change: transform, opacity;
      box-shadow: var(--shadow-xl);
    }

    .page-enter {
      transform: translateX(0);
      opacity: 1;
    }

    /* RTL-aware page transitions */
    .page-exit-left {
      transform: translateX(-20%);
      opacity: 0;
    }

    .page-enter-from-right {
      transform: translateX(20%);
      opacity: 0;
    }

    .page-exit-right {
      transform: translateX(20%);
      opacity: 0;
    }

    .page-enter-from-left {
      transform: translateX(-20%);
      opacity: 0;
    }

    /* Enhanced Card Styles with fixed backdrop-filter order */
    .card-enhanced {
      background: rgba(30, 41, 59, 0.8);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(148, 163, 184, 0.1);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow-xl);
      transition: all var(--transition-normal);
    }

    .card-enhanced:hover {
      transform: translateY(-4px);
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      border-color: rgba(148, 163, 184, 0.2);
    }

    /* PDF Flash Explorer Styles */
    .pdf-flash-container {
      position: relative;
      background: rgba(15, 23, 42, 0.95);
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--shadow-xl);
      border: 1px solid rgba(148, 163, 184, 0.1);
    }

    .pdf-page-canvas {
      max-width: 100%;
      height: auto;
      display: block;
      margin: 0 auto;
      border-radius: 8px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .pdf-controls {
      background: rgba(30, 41, 59, 0.9);
      -webkit-backdrop-filter: blur(8px);
      backdrop-filter: blur(8px);
      border-top: 1px solid rgba(148, 163, 184, 0.1);
      padding: 12px;
    }

    .pdf-thumbnail {
      width: 60px;
      height: 80px;
      border-radius: 4px;
      border: 2px solid transparent;
      cursor: pointer;
      transition: all var(--transition-normal);
      overflow: hidden;
    }

    .pdf-thumbnail:hover {
      border-color: var(--primary-500);
      transform: scale(1.05);
    }

    .pdf-thumbnail.active {
      border-color: var(--primary-600);
      box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.3);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      body { font-size: 14px; }
      .loading-spinner { width: 60px; height: 60px; }
      .pdf-thumbnail { width: 50px; height: 65px; }
    }

    /* Reduced Motion for Accessibility */
    @media (prefers-reduced-motion: reduce) {
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }

      .page-content {
        transition: none !important;
        transform: none !important;
      }

      .loading-content, .loading-spinner {
        animation: none !important;
      }

      .card-enhanced:hover {
        transform: none !important;
      }
    }

  </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.1.0"
  }
}
</script>
</head>
<body class="bg-slate-900 text-slate-100 dark font-arabic antialiased" dir="rtl">
  <!-- Enhanced Loading Screen with PDF Flash Explorer Features -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <h2 class="text-4xl font-bold text-gradient mb-3">🚀 محادثة الكتب التفاعلية بالذكاء الاصطناعي</h2>
      <p class="text-slate-400 text-lg mb-2">جاري تحويل تجربة القراءة مع مستكشف PDF Flash...</p>
      <p class="text-slate-500 text-sm mb-4">✨ مستكشف PDF Flash ✨ صور وفيديوهات تفاعلية ✨ إحصائيات حديثة</p>

      <div class="flex items-center justify-center space-x-3 space-x-reverse mb-4">
        <div class="w-3 h-3 bg-red-400 rounded-full animate-bounce bounce-delay-0"></div>
        <div class="w-3 h-3 bg-emerald-400 rounded-full animate-bounce bounce-delay-150"></div>
        <div class="w-3 h-3 bg-blue-400 rounded-full animate-bounce bounce-delay-300"></div>
        <div class="w-3 h-3 bg-purple-400 rounded-full animate-bounce bounce-delay-0"></div>
      </div>

      <p class="text-xs text-slate-600 mt-6">الإصدار 1.2.0 - مع مستكشف PDF Flash - يناير 2025</p>
    </div>
  </div>

  <!-- Main Application Container -->
  <div id="root" class="animate-fade-in"></div>

  <!-- Enhanced Scripts -->
  <script>
    // Performance monitoring
    window.performance.mark('app-start');

    // Enhanced loading screen management
    let loadingHidden = false;

    function hideLoadingScreen() {
      if (loadingHidden) return;
      loadingHidden = true;

      const loadingScreen = document.getElementById('loading-screen');
      if (loadingScreen) {
        loadingScreen.classList.add('fade-out');
        setTimeout(() => {
          loadingScreen.style.display = 'none';
          loadingScreen.remove();
        }, 800);
      }

      window.performance.mark('loading-screen-hidden');
      console.log('🎉 تم تحميل التطبيق مع مستكشف PDF Flash!');
    }

    // Hide loading screen when app is ready
    window.addEventListener('load', () => {
      setTimeout(hideLoadingScreen, 1500);
    });

    window.addEventListener('app-mounted', hideLoadingScreen);

    // Enhanced error handling
    window.addEventListener('error', (event) => {
      console.error('🚨 خطأ عام:', event.error);
      hideLoadingScreen();
    });

    window.addEventListener('unhandledrejection', (event) => {
      console.error('🚨 رفض وعد غير معالج:', event.reason);
      hideLoadingScreen();
    });

    // Initialize enhanced app metadata
    window.APP_INFO = {
      name: 'محادثة الكتب التفاعلية بالذكاء الاصطناعي',
      nameEn: 'AI Interactive Book Chat with PDF Flash Explorer',
      version: '1.2.0',
      lastUpdated: 'يناير 2025',
      buildDate: new Date().toISOString(),
      language: 'ar',
      direction: 'rtl',
      features: [
        'مستكشف PDF Flash المتقدم',
        'تحليل الكتب بالذكاء الاصطناعي',
        'عروض تقديمية بمحتوى حقيقي',
        'صور وفيديوهات تفاعلية',
        'إحصائيات حديثة وبيانات حقيقية',
        'تكامل المحادثة الصوتية',
        'دعم تنسيقات متعددة',
        'واجهة عربية كاملة مع RTL'
      ],
      newFeatures: [
        '📄 مستكشف PDF Flash مع معاينة سريعة',
        '🔍 تكبير وتصغير متقدم للصفحات',
        '📑 صور مصغرة للتنقل السريع',
        '⚡ تحميل سريع وسلس للصفحات',
        '🎯 تحديد النصوص والتفاعل معها',
        '💾 ذاكرة تخزين مؤقت محسنة'
      ]
    };

    console.log(`
    🚀 مرحباً بك في ${window.APP_INFO.name}!

    📋 الإصدار: ${window.APP_INFO.version}
    📅 آخر تحديث: ${window.APP_INFO.lastUpdated}

    🆕 الميزات الجديدة:
    ${window.APP_INFO.newFeatures.map(feature => `   ${feature}`).join('\n')}

    🌐 التطبيق متاح على: http://localhost:5173
    📖 ارفع أي كتاب واستمتع بمستكشف PDF Flash!
    `);
  </script>

  <!-- Main Application Script -->
  <script type="module" src="/index.tsx"></script>
</body>
</html>
