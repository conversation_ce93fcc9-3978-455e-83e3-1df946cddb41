
<!DOCTYPE html>
<html lang="ar" dir="rtl" class="scroll-smooth">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">

  <!-- Primary Meta Tags -->
  <title>🚀 محادثة الكتب التفاعلية بالذكاء الاصطناعي - مع مستكشف PDF Flash المتقدم | الإصدار 1.2.1</title>
  <meta name="title" content="محادثة الكتب التفاعلية بالذكاء الاصطناعي - مع مستكشف PDF Flash المتقدم">
  <meta name="description" content="🎯 منصة ثورية لتحليل الكتب وإنشاء عروض تقديمية بمحتوى حقيقي! ⚡ مستكشف PDF Flash متقدم ⚡ تحليل ذكي للكتب ⚡ صور وفيديوهات تفاعلية ⚡ إحصائيات حديثة ⚡ مصادر موثوقة ⚡ دعم عربي كامل مع RTL.">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="http://localhost:5173/">
  <meta property="og:title" content="🚀 محادثة الكتب التفاعلية بالذكاء الاصطناعي - مع مستكشف PDF Flash المتقدم">
  <meta property="og:description" content="منصة ثورية لإنشاء عروض تقديمية بمحتوى حقيقي من الكتب. مستكشف PDF Flash متقدم، تحليل ذكي، صور وفيديوهات تفاعلية، ودعم عربي كامل.">
  <meta property="og:image" content="https://source.unsplash.com/1200x630/?artificial-intelligence,books,presentation,pdf">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="http://localhost:5173/">
  <meta property="twitter:title" content="🚀 محادثة الكتب التفاعلية بالذكاء الاصطناعي - مع مستكشف PDF Flash">
  <meta property="twitter:description" content="منصة ثورية لإنشاء عروض تقديمية بمحتوى حقيقي من الكتب مع مستكشف PDF Flash متقدم">
  <meta property="twitter:image" content="https://source.unsplash.com/1200x630/?artificial-intelligence,books,presentation,pdf">

  <!-- Additional Meta Tags -->
  <meta name="keywords" content="الذكاء الاصطناعي, تحليل الكتب, عروض تقديمية, محتوى حقيقي, مستكشف PDF, PDF Flash Explorer, صور تفاعلية, فيديوهات تعليمية, إحصائيات حديثة, دعم عربي, RTL, AI, presentations, real content, book analysis">
  <meta name="author" content="AI Interactive Book Chat with PDF Flash Explorer">
  <meta name="robots" content="index, follow">
  <meta name="language" content="Arabic">
  <meta name="revisit-after" content="7 days">
  <meta name="theme-color" content="#0f172a">

  <!-- Favicon and App Icons -->
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚡</text></svg>">
  <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📚</text></svg>">
  <link rel="manifest" href="manifest.webmanifest">

  <!-- External Fonts with Arabic Support -->
  <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Noto+Sans+Arabic:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

  <!-- External Scripts -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // Enhanced Tailwind CSS configuration with RTL support and advanced features
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          fontFamily: {
            'sans': ['Noto Sans Arabic', 'Inter', 'system-ui', 'sans-serif'],
            'arabic': ['Noto Sans Arabic', 'Amiri', 'Tahoma', 'Arial', 'sans-serif'],
            'mono': ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace']
          },
          colors: {
            'primary': {
              50: '#eff6ff',
              100: '#dbeafe',
              200: '#bfdbfe',
              300: '#93c5fd',
              400: '#60a5fa',
              500: '#3b82f6',
              600: '#2563eb',
              700: '#1d4ed8',
              800: '#1e40af',
              900: '#1e3a8a'
            },
            'accent': {
              50: '#f0f9ff',
              100: '#e0f2fe',
              200: '#bae6fd',
              300: '#7dd3fc',
              400: '#38bdf8',
              500: '#0ea5e9',
              600: '#0284c7',
              700: '#0369a1',
              800: '#075985',
              900: '#0c4a6e'
            }
          },
          animation: {
            'fade-in': 'fadeIn 0.8s ease-in-out',
            'slide-up': 'slideUp 0.6s ease-out',
            'slide-in-right': 'slideInRight 0.6s ease-out',
            'slide-in-left': 'slideInLeft 0.6s ease-out',
            'scale-in': 'scaleIn 0.5s ease-out',
            'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            'bounce-slow': 'bounce 2s infinite',
            'spin-slow': 'spin 3s linear infinite',
            'float': 'float 3s ease-in-out infinite'
          },
          backdropBlur: {
            xs: '2px',
          }
        }
      }
    }

    // PDF.js worker configuration
    window.pdfjsWorkerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
  </script>

  <!-- External Libraries -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js" defer></script>
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js" defer></script>
  <style>
    /* Enhanced CSS Styles for AI Interactive Book Chat with PDF Flash Explorer - Version 1.2.1 */

    /* Root CSS Variables */
    :root {
      --primary-500: #0ea5e9;
      --primary-600: #0284c7;
      --primary-700: #0369a1;
      --background-primary: #0f172a;
      --background-secondary: #1e293b;
      --background-tertiary: #334155;
      --text-primary: #f1f5f9;
      --text-secondary: #cbd5e1;
      --border-radius: 12px;
      --transition-normal: 0.3s ease;
      --transition-slow: 0.6s ease;
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* Global Styles with RTL Support */
    * {
      box-sizing: border-box;
    }

    html {
      scroll-behavior: smooth;
      font-size: 16px;
      line-height: 1.6;
    }

    body {
      font-family: 'Noto Sans Arabic', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
      overflow-x: hidden;
      direction: rtl;
      background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 50%, #0c4a6e 100%);
      min-height: 100vh;
    }

    /* Enhanced Loading Screen */
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 50%, #0c4a6e 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }

    .loading-screen.fade-out {
      opacity: 0;
      pointer-events: none;
    }

    .loading-content {
      text-align: center;
      animation: float 3s ease-in-out infinite;
    }

    .loading-spinner {
      width: 80px;
      height: 80px;
      margin: 0 auto 24px;
      border: 4px solid #bae6fd;
      border-top: 4px solid var(--primary-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    /* Performance-optimized animations using will-change and transform3d */
    @keyframes spin {
      0% { transform: rotate3d(0, 0, 1, 0deg); }
      100% { transform: rotate3d(0, 0, 1, 360deg); }
    }

    @keyframes float {
      0%, 100% { transform: translate3d(0, 0px, 0); }
      50% { transform: translate3d(0, -20px, 0); }
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translate3d(0, 20px, 0); }
      to { opacity: 1; transform: translate3d(0, 0, 0); }
    }

    @keyframes slideUp {
      from { opacity: 0; transform: translate3d(0, 30px, 0); }
      to { opacity: 1; transform: translate3d(0, 0, 0); }
    }

    @keyframes slideInRight {
      from { opacity: 0; transform: translate3d(30px, 0, 0); }
      to { opacity: 1; transform: translate3d(0, 0, 0); }
    }

    @keyframes slideInLeft {
      from { opacity: 0; transform: translate3d(-30px, 0, 0); }
      to { opacity: 1; transform: translate3d(0, 0, 0); }
    }

    @keyframes scaleIn {
      from { opacity: 0; transform: scale3d(0.9, 0.9, 1); }
      to { opacity: 1; transform: scale3d(1, 1, 1); }
    }

    /* Animation delay utilities */
    .bounce-delay-0 { animation-delay: 0ms; }
    .bounce-delay-150 { animation-delay: 150ms; }
    .bounce-delay-300 { animation-delay: 300ms; }

    /* Page animations */
    .animate-fade-in {
      animation: fadeIn 0.8s ease-in-out;
    }

    .animate-slide-up {
      animation: slideUp 0.6s ease-out;
    }

    .animate-slide-in-right {
      animation: slideInRight 0.6s ease-out;
    }

    .animate-slide-in-left {
      animation: slideInLeft 0.6s ease-out;
    }

    .animate-scale-in {
      animation: scaleIn 0.5s ease-out;
    }

    /* Enhanced Mermaid Diagram Styles */
    .mermaid {
      background: transparent;
      border-radius: var(--border-radius);
      padding: 16px;
      margin: 16px 0;
      direction: ltr; /* Keep diagrams LTR */
      box-shadow: var(--shadow-lg);
      border: 1px solid rgba(148, 163, 184, 0.1);
    }

    .mermaid svg {
      max-width: 100%;
      height: auto;
      background: transparent;
      border-radius: var(--border-radius);
    }

    /* Enhanced Page Transition Styles with RTL Support */
    .page-content {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      transition: transform var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1),
                  opacity var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: var(--border-radius);
      overflow: hidden;
      will-change: transform, opacity;
      box-shadow: var(--shadow-xl);
    }

    .page-enter {
      transform: translate3d(0, 0, 0);
      opacity: 1;
    }

    /* RTL-aware page transitions */
    .page-exit-left {
      transform: translate3d(-20%, 0, 0);
      opacity: 0;
    }

    .page-enter-from-right {
      transform: translate3d(20%, 0, 0);
      opacity: 0;
    }

    .page-exit-right {
      transform: translate3d(20%, 0, 0);
      opacity: 0;
    }

    .page-enter-from-left {
      transform: translate3d(-20%, 0, 0);
      opacity: 0;
    }

    /* Enhanced Card Styles with fixed backdrop-filter order */
    .card-enhanced {
      background: rgba(30, 41, 59, 0.8);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(148, 163, 184, 0.1);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow-xl);
      transition: all var(--transition-normal);
    }

    .card-enhanced:hover {
      transform: translate3d(0, -4px, 0);
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      border-color: rgba(148, 163, 184, 0.2);
    }

    /* PDF Flash Explorer Styles */
    .pdf-flash-container {
      position: relative;
      background: rgba(15, 23, 42, 0.95);
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--shadow-xl);
      border: 1px solid rgba(148, 163, 184, 0.1);
    }

    .pdf-page-canvas {
      max-width: 100%;
      height: auto;
      display: block;
      margin: 0 auto;
      border-radius: 8px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .pdf-controls {
      background: rgba(30, 41, 59, 0.9);
      -webkit-backdrop-filter: blur(8px);
      backdrop-filter: blur(8px);
      border-top: 1px solid rgba(148, 163, 184, 0.1);
      padding: 12px;
    }

    .pdf-thumbnail {
      width: 60px;
      height: 80px;
      border-radius: 4px;
      border: 2px solid transparent;
      cursor: pointer;
      transition: all var(--transition-normal);
      overflow: hidden;
      position: relative;
    }

    .pdf-thumbnail:hover {
      border-color: var(--primary-500);
      transform: scale3d(1.05, 1.05, 1);
    }

    .pdf-thumbnail.active {
      border-color: var(--primary-600);
      box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.3);
    }

  </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.1.0"
  }
}
</script>
</head>
<body class="bg-slate-900 text-slate-100">
  <!-- Enhanced Loading Screen -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <h2 class="text-2xl font-bold text-white mb-4 animate-pulse">
        ⚡ محادثة الكتب التفاعلية بالذكاء الاصطناعي
      </h2>
      <p class="text-lg text-blue-200 mb-6">
        مع مستكشف PDF Flash المتقدم
      </p>
      <div class="flex justify-center space-x-2 rtl:space-x-reverse">
        <div class="w-3 h-3 bg-blue-400 rounded-full animate-bounce bounce-delay-0"></div>
        <div class="w-3 h-3 bg-blue-400 rounded-full animate-bounce bounce-delay-150"></div>
        <div class="w-3 h-3 bg-blue-400 rounded-full animate-bounce bounce-delay-300"></div>
      </div>
      <p class="text-sm text-slate-300 mt-4">
        الإصدار 1.2.1 - تحليل ذكي للكتب مع دعم عربي كامل
      </p>
    </div>
  </div>

  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>

  <script>
    // Enhanced loading screen with fade out
    document.addEventListener('DOMContentLoaded', function() {
      const loadingScreen = document.getElementById('loading-screen');

      // Hide loading screen after React app loads
      setTimeout(() => {
        if (loadingScreen) {
          loadingScreen.classList.add('fade-out');
          setTimeout(() => {
            loadingScreen.style.display = 'none';
          }, 800);
        }
      }, 2000);
    });

    // Service Worker Registration for PWA
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
          .then(function(registration) {
            console.log('ServiceWorker registration successful with scope: ', registration.scope);
          }, function(err) {
            console.log('ServiceWorker registration failed: ', err);
          });
      });
    }

    // Enhanced error handling
    window.addEventListener('error', function(e) {
      console.error('Global error:', e.error);
    });

    window.addEventListener('unhandledrejection', function(e) {
      console.error('Unhandled promise rejection:', e.reason);
    });
  </script>
</body>
</html>
