
<!DOCTYPE html>
<html lang="ar" dir="rtl" class="scroll-smooth">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">

  <!-- Primary Meta Tags -->
  <title>محادثة الكتب التفاعلية بالذكاء الاصطناعي - حوّل تجربة القراءة | آخر تحديث يناير 2025</title>
  <meta name="title" content="محادثة الكتب التفاعلية بالذكاء الاصطناعي - حوّل تجربة القراءة">
  <meta name="description" content="منصة ثورية لتحليل الكتب بالذكاء الاصطناعي. ارفع ملفات PDF أو DOCX أو TXT وحوّل قراءتك مع استخراج المواضيع الذكي والعروض التفاعلية والمحادثة الصوتية. آخر تحديث يناير 2025.">

  <!-- External Fonts with Arabic Support -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Noto+Sans+Arabic:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

  <!-- External Scripts -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // Tailwind CSS configuration with RTL support
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          fontFamily: {
            'sans': ['Noto Sans Arabic', 'Inter', 'system-ui', 'sans-serif'],
            'arabic': ['Noto Sans Arabic', 'Tahoma', 'Arial', 'sans-serif']
          }
        }
      }
    }

    // PDF.js worker configuration
    window.pdfjsWorkerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
  </script>

  <!-- External Libraries -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js" defer></script>
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js" defer></script>

  <style>
    /* Enhanced CSS Styles - Last Updated January 2025 */

    /* Root CSS Variables */
    :root {
      --primary-500: #0ea5e9;
      --background-primary: #0f172a;
      --background-secondary: #1e293b;
      --text-primary: #f1f5f9;
      --border-radius: 12px;
      --transition-normal: 0.3s ease;
    }

    /* Global Styles with RTL Support */
    * {
      box-sizing: border-box;
    }

    html {
      scroll-behavior: smooth;
      font-size: 16px;
      line-height: 1.6;
    }

    body {
      font-family: 'Noto Sans Arabic', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
      overflow-x: hidden;
      direction: rtl;
    }

    /* Enhanced Loading Screen */
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 50%, #0c4a6e 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }

    .loading-screen.fade-out {
      opacity: 0;
      pointer-events: none;
    }

    .loading-content {
      text-align: center;
      animation: float 3s ease-in-out infinite;
    }

    .loading-spinner {
      width: 80px;
      height: 80px;
      margin: 0 auto 24px;
      border: 4px solid #bae6fd;
      border-top: 4px solid var(--primary-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    /* Animations */
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
    }

    /* Animation delay utilities */
    .bounce-delay-0 { animation-delay: 0ms; }
    .bounce-delay-150 { animation-delay: 150ms; }
    .bounce-delay-300 { animation-delay: 300ms; }

    /* Enhanced Mermaid Diagram Styles */
    .mermaid {
      background: transparent;
      border-radius: var(--border-radius);
      padding: 16px;
      margin: 16px 0;
      direction: ltr; /* Keep diagrams LTR */
    }

    .mermaid svg {
      max-width: 100%;
      height: auto;
      background: transparent;
      border-radius: var(--border-radius);
    }

    /* Enhanced Page Transition Styles with RTL Support */
    .page-content {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      transform-style: preserve-3d;
      backface-visibility: hidden;
      border-radius: var(--border-radius);
      overflow: hidden;
      will-change: transform, opacity;
    }

    .page-enter {
      transform: rotateY(0deg) translateZ(0);
      opacity: 1;
    }

    /* RTL-aware page transitions */
    .page-exit-left {
      transform-origin: right center;
      transform: rotateY(-90deg) translateZ(-50px);
      opacity: 0.7;
    }

    .page-enter-from-right {
      transform-origin: right center;
      transform: rotateY(90deg) translateZ(-50px);
      opacity: 0.7;
    }

    .page-exit-right {
      transform-origin: left center;
      transform: rotateY(90deg) translateZ(-50px);
      opacity: 0.7;
    }

    .page-enter-from-left {
      transform-origin: left center;
      transform: rotateY(-90deg) translateZ(-50px);
      opacity: 0.7;
    }

    /* Enhanced Typography */
    .text-gradient {
      background: linear-gradient(135deg, #38bdf8, #7dd3fc, #06b6d4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      body { font-size: 14px; }
      .loading-spinner { width: 60px; height: 60px; }
    }

    /* Reduced Motion for Accessibility */
    @media (prefers-reduced-motion: reduce) {
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }

      .page-content {
        transition: none !important;
        transform: none !important;
      }

      .loading-content, .loading-spinner {
        animation: none !important;
      }
    }

  </style>

<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.1.0"
  }
}
</script>
</head>
<body class="bg-slate-900 text-slate-100 dark font-arabic antialiased" dir="rtl">
  <!-- Enhanced Loading Screen -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <h2 class="text-3xl font-bold text-gradient mb-2">محادثة الكتب التفاعلية بالذكاء الاصطناعي</h2>
      <p class="text-slate-400 text-lg mb-4">جاري تحويل تجربة القراءة الخاصة بك...</p>
      <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <div class="w-2 h-2 bg-sky-400 rounded-full animate-bounce bounce-delay-0"></div>
        <div class="w-2 h-2 bg-sky-500 rounded-full animate-bounce bounce-delay-150"></div>
        <div class="w-2 h-2 bg-sky-600 rounded-full animate-bounce bounce-delay-300"></div>
      </div>
      <p class="text-xs text-slate-500 mt-4">آخر تحديث: يناير 2025</p>
    </div>
  </div>

  <!-- Main Application Container -->
  <div id="root" class="animate-fade-in"></div>

  <!-- Enhanced Scripts -->
  <script>
    // Performance monitoring
    window.performance.mark('app-start');

    // Enhanced service worker registration
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', async () => {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js');
          console.log('✅ تم تسجيل Service Worker بنجاح:', registration.scope);
        } catch (error) {
          console.error('❌ فشل في تسجيل Service Worker:', error);
        }
      });
    }

    // Enhanced loading screen management
    let loadingHidden = false;

    function hideLoadingScreen() {
      if (loadingHidden) return;
      loadingHidden = true;

      const loadingScreen = document.getElementById('loading-screen');
      if (loadingScreen) {
        loadingScreen.classList.add('fade-out');
        setTimeout(() => {
          loadingScreen.style.display = 'none';
          loadingScreen.remove();
        }, 800);
      }

      window.performance.mark('loading-screen-hidden');
    }

    // Hide loading screen when app is ready
    window.addEventListener('load', () => {
      setTimeout(hideLoadingScreen, 1200);
    });

    window.addEventListener('app-mounted', hideLoadingScreen);

    // Enhanced error handling
    window.addEventListener('error', (event) => {
      console.error('🚨 خطأ عام:', event.error);
      hideLoadingScreen();
    });

    window.addEventListener('unhandledrejection', (event) => {
      console.error('🚨 رفض وعد غير معالج:', event.reason);
      hideLoadingScreen();
    });

    // Theme detection
    function updateTheme() {
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      document.documentElement.classList.toggle('dark', isDark);
      document.body.classList.toggle('dark', isDark);
    }

    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', updateTheme);
    updateTheme();

    // Initialize app metadata
    window.APP_INFO = {
      name: 'محادثة الكتب التفاعلية بالذكاء الاصطناعي',
      nameEn: 'AI Interactive Book Chat',
      version: '1.0.1',
      lastUpdated: 'يناير 2025',
      buildDate: new Date().toISOString(),
      language: 'ar',
      direction: 'rtl',
      features: [
        'تحليل الكتب بالذكاء الاصطناعي',
        'العروض التقديمية التفاعلية',
        'تكامل المحادثة الصوتية',
        'دعم تنسيقات متعددة',
        'تطبيق ويب تقدمي',
        'وظائف غير متصلة بالإنترنت'
      ]
    };

    console.log('🚀 تم تهيئة محادثة الكتب التفاعلية بالذكاء الاصطناعي:', window.APP_INFO);
  </script>

  <!-- Main Application Script -->
  <script type="module" src="/index.tsx"></script>
</body>
</html>
