
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Interactive Textbook</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // pdf.js worker configuration
    window.pdfjsWorkerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
  </script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
  <style>
    /* Ensure SVGs generated by Me<PERSON> are responsive and styled correctly */
    .mermaid svg {
      max-width: 100%;
      height: auto;
    }

    /* Page transition styles for BookViewer */
    .page-content {
      transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
      will-change: opacity, transform;
      position: absolute; /* Needed for smooth transition in the same spot */
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }

    .page-enter {
      opacity: 1;
      transform: translateX(0%);
    }

    /* When navigating "next" */
    .page-exit-left {
      opacity: 0;
      transform: translateX(-20%); /* Slide out to the left */
    }
    .page-enter-from-right { /* New page coming in */
      opacity: 0;
      transform: translateX(20%);
    }

    /* When navigating "prev" */
    .page-exit-right {
      opacity: 0;
      transform: translateX(20%); /* Slide out to the right */
    }
    .page-enter-from-left { /* New page coming in */
      opacity: 0;
      transform: translateX(-20%);
    }

    /* Ensure the BookViewer's main content area uses its full height for scroll */
    main.md\\:col-span-8 .prose {
        /* The h-full on the page-content div takes care of this mostly,
           but this is a fallback if the parent of prose restricts height.
           The current `h-full` in BookViewer's page-content should work.
        */
    }

    /* TTS (Text-to-Speech) Styles */
    .tts-reader {
      background: rgba(30, 41, 59, 0.9);
      border-radius: 12px;
      border: 1px solid rgba(148, 163, 184, 0.1);
      padding: 16px;
    }

    .tts-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      align-items: center;
    }

    .tts-slider {
      -webkit-appearance: none;
      appearance: none;
      height: 6px;
      background: rgba(51, 65, 85, 0.8);
      border-radius: 3px;
      outline: none;
      transition: all 0.3s ease;
    }

    .tts-slider::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      background: #0ea5e9;
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .tts-slider::-webkit-slider-thumb:hover {
      background: #38bdf8;
      transform: scale(1.1);
    }

    .tts-text-highlight {
      background: linear-gradient(120deg, rgba(255, 235, 59, 0.3) 0%, rgba(255, 193, 7, 0.3) 100%);
      color: #f1f5f9;
      padding: 2px 4px;
      border-radius: 4px;
      transition: all 0.3s ease;
      animation: pulse 1.5s ease-in-out infinite;
    }

    .tts-language-indicator {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      padding: 4px 8px;
      background: rgba(14, 165, 233, 0.1);
      border: 1px solid rgba(14, 165, 233, 0.3);
      border-radius: 6px;
      font-size: 12px;
      color: #38bdf8;
    }

    /* Responsive TTS Styles */
    @media (max-width: 768px) {
      .tts-controls {
        flex-direction: column;
        align-items: stretch;
      }
    }

  </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.1.0"
  }
}
</script>
</head>
<body class="bg-slate-900 text-slate-100">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
<link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
