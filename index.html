
<!DOCTYPE html>
<html lang="ar" dir="rtl" class="scroll-smooth">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">

  <!-- Primary Meta Tags -->
  <title>🚀 محادثة الكتب التفاعلية بالذكاء الاصطناعي - عروض تقديمية بمحتوى حقيقي | الإصدار 1.1.0</title>
  <meta name="title" content="محادثة الكتب التفاعلية بالذكاء الاصطناعي - عروض تقديمية بمحتوى حقيقي">
  <meta name="description" content="🎯 منصة ثورية لتحليل الكتب وإنشاء عروض تقديمية بمحتوى حقيقي! ✨ صور وفيديوهات تفاعلية ✨ إحصائيات حديثة ✨ مصادر موثوقة ✨ دعم عربي كامل. ارفع أي كتاب واحصل على عرض تقديمي متقدم في دقائق!">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="http://localhost:5173/">
  <meta property="og:title" content="🚀 محادثة الكتب التفاعلية بالذكاء الاصطناعي - عروض تقديمية بمحتوى حقيقي">
  <meta property="og:description" content="منصة ثورية لإنشاء عروض تقديمية بمحتوى حقيقي من الكتب. صور وفيديوهات تفاعلية، إحصائيات حديثة، ودعم عربي كامل.">
  <meta property="og:image" content="https://source.unsplash.com/1200x630/?artificial-intelligence,books,presentation">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="http://localhost:5173/">
  <meta property="twitter:title" content="🚀 محادثة الكتب التفاعلية بالذكاء الاصطناعي">
  <meta property="twitter:description" content="منصة ثورية لإنشاء عروض تقديمية بمحتوى حقيقي من الكتب">
  <meta property="twitter:image" content="https://source.unsplash.com/1200x630/?artificial-intelligence,books,presentation">

  <!-- Additional Meta Tags -->
  <meta name="keywords" content="الذكاء الاصطناعي, تحليل الكتب, عروض تقديمية, محتوى حقيقي, صور تفاعلية, فيديوهات تعليمية, إحصائيات حديثة, دعم عربي, AI, presentations, real content">
  <meta name="author" content="AI Interactive Book Chat">
  <meta name="robots" content="index, follow">
  <meta name="language" content="Arabic">
  <meta name="revisit-after" content="7 days">

  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
  <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📚</text></svg>">

  <!-- External Fonts with Arabic Support -->
  <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Noto+Sans+Arabic:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

  <!-- External Scripts -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // Tailwind CSS configuration with RTL support and enhanced features
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          fontFamily: {
            'sans': ['Noto Sans Arabic', 'Inter', 'system-ui', 'sans-serif'],
            'arabic': ['Noto Sans Arabic', 'Tahoma', 'Arial', 'sans-serif'],
            'mono': ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace']
          },
          colors: {
            'primary': {
              50: '#eff6ff',
              100: '#dbeafe',
              200: '#bfdbfe',
              300: '#93c5fd',
              400: '#60a5fa',
              500: '#3b82f6',
              600: '#2563eb',
              700: '#1d4ed8',
              800: '#1e40af',
              900: '#1e3a8a'
            }
          },
          animation: {
            'fade-in': 'fadeIn 0.8s ease-in-out',
            'slide-up': 'slideUp 0.6s ease-out',
            'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            'bounce-slow': 'bounce 2s infinite',
            'spin-slow': 'spin 3s linear infinite'
          }
        }
      }
    }

    // PDF.js worker configuration
    window.pdfjsWorkerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
  </script>

  <!-- External Libraries -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js" defer></script>
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js" defer></script>
  <style>
    /* Enhanced CSS Styles for AI Interactive Book Chat - Version 1.1.0 */

    /* Root CSS Variables */
    :root {
      --primary-50: #eff6ff;
      --primary-500: #0ea5e9;
      --primary-600: #0284c7;
      --primary-700: #0369a1;
      --background-primary: #0f172a;
      --background-secondary: #1e293b;
      --background-tertiary: #334155;
      --text-primary: #f1f5f9;
      --text-secondary: #cbd5e1;
      --text-muted: #64748b;
      --border-radius: 12px;
      --transition-normal: 0.3s ease;
      --transition-slow: 0.6s ease;
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* Global Styles with RTL Support */
    * {
      box-sizing: border-box;
    }

    html {
      scroll-behavior: smooth;
      font-size: 16px;
      line-height: 1.6;
    }

    body {
      font-family: 'Noto Sans Arabic', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
      overflow-x: hidden;
      direction: rtl;
      background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 50%, #0c4a6e 100%);
      min-height: 100vh;
    }

    /* Enhanced Loading Screen */
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 50%, #0c4a6e 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }

    .loading-screen.fade-out {
      opacity: 0;
      pointer-events: none;
    }

    .loading-content {
      text-align: center;
      animation: float 3s ease-in-out infinite;
    }

    .loading-spinner {
      width: 80px;
      height: 80px;
      margin: 0 auto 24px;
      border: 4px solid #bae6fd;
      border-top: 4px solid var(--primary-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    /* Enhanced Animations */
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes slideUp {
      from { opacity: 0; transform: translateY(30px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes slideInRight {
      from { opacity: 0; transform: translateX(30px); }
      to { opacity: 1; transform: translateX(0); }
    }

    @keyframes slideInLeft {
      from { opacity: 0; transform: translateX(-30px); }
      to { opacity: 1; transform: translateX(0); }
    }

    @keyframes scaleIn {
      from { opacity: 0; transform: scale(0.9); }
      to { opacity: 1; transform: scale(1); }
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    @keyframes bounce {
      0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
      40%, 43% { transform: translateY(-30px); }
      70% { transform: translateY(-15px); }
      90% { transform: translateY(-4px); }
    }

    /* Animation delay utilities */
    .bounce-delay-0 { animation-delay: 0ms; }
    .bounce-delay-150 { animation-delay: 150ms; }
    .bounce-delay-300 { animation-delay: 300ms; }

    /* Page animations */
    .animate-fade-in {
      animation: fadeIn 0.8s ease-in-out;
    }

    .animate-slide-up {
      animation: slideUp 0.6s ease-out;
    }

    .animate-slide-in-right {
      animation: slideInRight 0.6s ease-out;
    }

    .animate-slide-in-left {
      animation: slideInLeft 0.6s ease-out;
    }

    .animate-scale-in {
      animation: scaleIn 0.5s ease-out;
    }

    /* Enhanced Mermaid Diagram Styles */
    .mermaid {
      background: transparent;
      border-radius: var(--border-radius);
      padding: 16px;
      margin: 16px 0;
      direction: ltr; /* Keep diagrams LTR */
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--background-tertiary);
    }

    .mermaid svg {
      max-width: 100%;
      height: auto;
      background: transparent;
      border-radius: var(--border-radius);
    }

    /* Enhanced Page Transition Styles with RTL Support */
    .page-content {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      transition: transform var(--transition-slow) cubic-bezier(0.4, 0, 0.2, 1),
                  opacity var(--transition-slow) cubic-bezier(0.4, 0, 0.2, 1);
      transform-style: preserve-3d;
      backface-visibility: hidden;
      border-radius: var(--border-radius);
      overflow: hidden;
      will-change: transform, opacity;
      box-shadow: var(--shadow-xl);
    }

    .page-enter {
      transform: rotateY(0deg) translateZ(0);
      opacity: 1;
    }

    /* RTL-aware page transitions */
    .page-exit-left {
      transform-origin: right center;
      transform: rotateY(-90deg) translateZ(-50px);
      opacity: 0.7;
    }

    .page-enter-from-right {
      transform-origin: right center;
      transform: rotateY(90deg) translateZ(-50px);
      opacity: 0.7;
    }

    .page-exit-right {
      transform-origin: left center;
      transform: rotateY(90deg) translateZ(-50px);
      opacity: 0.7;
    }

    .page-enter-from-left {
      transform-origin: left center;
      transform: rotateY(-90deg) translateZ(-50px);
      opacity: 0.7;
    }

    /* Enhanced Typography */
    .text-gradient {
      background: linear-gradient(135deg, #38bdf8, #7dd3fc, #06b6d4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .text-gradient-primary {
      background: linear-gradient(135deg, var(--primary-500), var(--primary-600), var(--primary-700));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    /* Enhanced Button Styles */
    .btn-enhanced {
      position: relative;
      overflow: hidden;
      transition: all var(--transition-normal);
      transform: translateY(0);
    }

    .btn-enhanced:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-xl);
    }

    .btn-enhanced:active {
      transform: translateY(0);
    }

    .btn-enhanced::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .btn-enhanced:hover::before {
      left: 100%;
    }

    /* Enhanced Card Styles */
    .card-enhanced {
      background: rgba(30, 41, 59, 0.8);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(148, 163, 184, 0.1);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow-xl);
      transition: all var(--transition-normal);
    }

    .card-enhanced:hover {
      transform: translateY(-4px);
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      border-color: rgba(148, 163, 184, 0.2);
    }

    /* Enhanced Scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: var(--background-secondary);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background: var(--primary-500);
      border-radius: 4px;
      transition: background var(--transition-normal);
    }

    ::-webkit-scrollbar-thumb:hover {
      background: var(--primary-600);
    }

    /* Enhanced Focus Styles */
    .focus-enhanced:focus {
      outline: none;
      ring: 2px;
      ring-color: var(--primary-500);
      ring-opacity: 0.5;
      border-color: var(--primary-500);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      body { font-size: 14px; }
      .loading-spinner { width: 60px; height: 60px; }
      .card-enhanced { margin: 8px; }
    }

    @media (max-width: 480px) {
      body { font-size: 13px; }
      .loading-content h2 { font-size: 1.5rem; }
      .loading-content p { font-size: 0.9rem; }
    }

    /* High Contrast Mode */
    @media (prefers-contrast: high) {
      :root {
        --background-primary: #000000;
        --background-secondary: #1a1a1a;
        --text-primary: #ffffff;
        --primary-500: #00bfff;
      }
    }

    /* Reduced Motion for Accessibility */
    @media (prefers-reduced-motion: reduce) {
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }

      .page-content {
        transition: none !important;
        transform: none !important;
      }

      .loading-content, .loading-spinner {
        animation: none !important;
      }

      .btn-enhanced:hover {
        transform: none !important;
      }

      .card-enhanced:hover {
        transform: none !important;
      }
    }

    /* Print Styles */
    @media print {
      .loading-screen {
        display: none !important;
      }

      body {
        background: white !important;
        color: black !important;
      }

      .card-enhanced {
        background: white !important;
        border: 1px solid #ccc !important;
        box-shadow: none !important;
      }
    }

  </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.1.0"
  }
}
</script>
</head>
<body class="bg-slate-900 text-slate-100 dark font-arabic antialiased" dir="rtl">
  <!-- Enhanced Loading Screen with Real Content Features -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <h2 class="text-4xl font-bold text-gradient-primary mb-3">🚀 محادثة الكتب التفاعلية بالذكاء الاصطناعي</h2>
      <p class="text-slate-400 text-lg mb-2">جاري تحويل تجربة القراءة الخاصة بك مع محتوى حقيقي...</p>
      <p class="text-slate-500 text-sm mb-4">✨ صور وفيديوهات تفاعلية ✨ إحصائيات حديثة ✨ مصادر موثوقة</p>

      <div class="flex items-center justify-center space-x-3 space-x-reverse mb-4">
        <div class="w-3 h-3 bg-emerald-400 rounded-full animate-bounce bounce-delay-0"></div>
        <div class="w-3 h-3 bg-blue-400 rounded-full animate-bounce bounce-delay-150"></div>
        <div class="w-3 h-3 bg-purple-400 rounded-full animate-bounce bounce-delay-300"></div>
        <div class="w-3 h-3 bg-red-400 rounded-full animate-bounce bounce-delay-0"></div>
      </div>

      <div class="space-y-2 text-xs text-slate-500">
        <div class="flex items-center justify-center space-x-2 space-x-reverse">
          <div class="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
          <span>البحث عن محتوى حقيقي ومحدث</span>
        </div>
        <div class="flex items-center justify-center space-x-2 space-x-reverse">
          <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <span>جلب صور وفيديوهات تفاعلية</span>
        </div>
        <div class="flex items-center justify-center space-x-2 space-x-reverse">
          <div class="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
          <span>جمع إحصائيات من مصادر موثوقة</span>
        </div>
      </div>

      <p class="text-xs text-slate-600 mt-6">الإصدار 1.1.0 - آخر تحديث: يناير 2025</p>
    </div>
  </div>

  <!-- Main Application Container -->
  <div id="root" class="animate-fade-in"></div>

  <!-- Enhanced Scripts with Performance Monitoring -->
  <script>
    // Performance monitoring and app initialization
    window.performance.mark('app-start');

    // Enhanced loading screen management with feature highlights
    let loadingHidden = false;

    function hideLoadingScreen() {
      if (loadingHidden) return;
      loadingHidden = true;

      const loadingScreen = document.getElementById('loading-screen');
      if (loadingScreen) {
        loadingScreen.classList.add('fade-out');
        setTimeout(() => {
          loadingScreen.style.display = 'none';
          loadingScreen.remove();
        }, 800);
      }

      window.performance.mark('loading-screen-hidden');
      console.log('🎉 تم تحميل التطبيق بنجاح مع الميزات المحسنة!');
    }

    // Hide loading screen when app is ready
    window.addEventListener('load', () => {
      setTimeout(hideLoadingScreen, 1500); // Slightly longer to show new features
    });

    window.addEventListener('app-mounted', hideLoadingScreen);

    // Enhanced error handling with Arabic support
    window.addEventListener('error', (event) => {
      console.error('🚨 خطأ عام:', event.error);
      hideLoadingScreen();
    });

    window.addEventListener('unhandledrejection', (event) => {
      console.error('🚨 رفض وعد غير معالج:', event.reason);
      hideLoadingScreen();
    });

    // Initialize enhanced app metadata
    window.APP_INFO = {
      name: 'محادثة الكتب التفاعلية بالذكاء الاصطناعي',
      nameEn: 'AI Interactive Book Chat',
      version: '1.1.0',
      lastUpdated: 'يناير 2025',
      buildDate: new Date().toISOString(),
      language: 'ar',
      direction: 'rtl',
      features: [
        'تحليل الكتب بالذكاء الاصطناعي المتقدم',
        'عروض تقديمية بمحتوى حقيقي ومحدث',
        'صور وفيديوهات تفاعلية من مصادر موثوقة',
        'إحصائيات حديثة وبيانات حقيقية',
        'تكامل المحادثة الصوتية المتطورة',
        'دعم تنسيقات متعددة (PDF, DOCX, TXT)',
        'واجهة عربية كاملة مع RTL',
        'تطبيق ويب تقدمي مع وظائف غير متصلة',
        'بحث ذكي على الويب للمحتوى الحقيقي',
        'مخططات بصرية محسنة بالبيانات الفعلية'
      ],
      newFeatures: [
        '🔍 بحث تلقائي عن محتوى حقيقي ومحدث',
        '🖼️ صور من Unsplash وPixabay تعكس الموضوع',
        '🎥 فيديوهات تعليمية من YouTube',
        '📊 إحصائيات حديثة من مصادر موثوقة',
        '🌍 أمثلة من العالم الحقيقي',
        '📚 مصادر ومراجع لجميع المعلومات',
        '🎯 عناصر تفاعلية متقدمة',
        '✨ واجهة محسنة مع رسوم متحركة'
      ]
    };

    // Enhanced console welcome message
    console.log(`
    🚀 مرحباً بك في ${window.APP_INFO.name}!

    📋 الإصدار: ${window.APP_INFO.version}
    📅 آخر تحديث: ${window.APP_INFO.lastUpdated}

    🎯 الميزات الجديدة:
    ${window.APP_INFO.newFeatures.map(feature => `   ${feature}`).join('\n')}

    🌐 التطبيق متاح على: http://localhost:5173
    📖 ارفع أي كتاب واستمتع بعروض تقديمية متقدمة!
    `);

    // Performance metrics
    window.addEventListener('load', () => {
      setTimeout(() => {
        const loadTime = performance.now();
        console.log(`⚡ وقت التحميل: ${Math.round(loadTime)}ms`);

        // Log memory usage if available
        if (performance.memory) {
          console.log(`💾 استخدام الذاكرة: ${Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)}MB`);
        }
      }, 1000);
    });

    // Service Worker registration for PWA features
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => {
            console.log('🔧 Service Worker مسجل بنجاح:', registration.scope);
          })
          .catch(error => {
            console.log('❌ فشل تسجيل Service Worker:', error);
          });
      });
    }
  </script>

  <!-- Main Application Script -->
  <script type="module" src="/index.tsx"></script>
</body>
</html>
