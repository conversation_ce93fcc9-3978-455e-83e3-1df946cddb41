
<!DOCTYPE html>
<html lang="ar" dir="rtl" class="scroll-smooth">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">

  <!-- Primary Meta Tags -->
  <title>🚀 محادثة الكتب التفاعلية بالذكاء الاصطناعي - مع مستكشف PDF Flash المتقدم | الإصدار 1.2.1</title>
  <meta name="title" content="محادثة الكتب التفاعلية بالذكاء الاصطناعي - مع مستكشف PDF Flash المتقدم">
  <meta name="description" content="🎯 منصة ثورية لتحليل الكتب وإنشاء عروض تقديمية بمحتوى حقيقي! ⚡ مستكشف PDF Flash متقدم ⚡ تحليل ذكي للكتب ⚡ صور وفيديوهات تفاعلية ⚡ إحصائيات حديثة ⚡ مصادر موثوقة ⚡ دعم عربي كامل مع RTL.">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="http://localhost:5173/">
  <meta property="og:title" content="🚀 محادثة الكتب التفاعلية بالذكاء الاصطناعي - مع مستكشف PDF Flash المتقدم">
  <meta property="og:description" content="منصة ثورية لإنشاء عروض تقديمية بمحتوى حقيقي من الكتب. مستكشف PDF Flash متقدم، تحليل ذكي، صور وفيديوهات تفاعلية، ودعم عربي كامل.">
  <meta property="og:image" content="https://source.unsplash.com/1200x630/?artificial-intelligence,books,presentation,pdf">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="http://localhost:5173/">
  <meta property="twitter:title" content="🚀 محادثة الكتب التفاعلية بالذكاء الاصطناعي - مع مستكشف PDF Flash">
  <meta property="twitter:description" content="منصة ثورية لإنشاء عروض تقديمية بمحتوى حقيقي من الكتب مع مستكشف PDF Flash متقدم">
  <meta property="twitter:image" content="https://source.unsplash.com/1200x630/?artificial-intelligence,books,presentation,pdf">

  <!-- Additional Meta Tags -->
  <meta name="keywords" content="الذكاء الاصطناعي, تحليل الكتب, عروض تقديمية, محتوى حقيقي, مستكشف PDF, PDF Flash Explorer, صور تفاعلية, فيديوهات تعليمية, إحصائيات حديثة, دعم عربي, RTL, AI, presentations, real content, book analysis">
  <meta name="author" content="AI Interactive Book Chat with PDF Flash Explorer">
  <meta name="robots" content="index, follow">
  <meta name="language" content="Arabic">
  <meta name="revisit-after" content="7 days">
  <meta name="theme-color" content="#0f172a">

  <!-- Favicon and App Icons -->
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚡</text></svg>">
  <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📚</text></svg>">
  <link rel="manifest" href="manifest.webmanifest">

  <!-- External Fonts with Arabic Support -->
  <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Noto+Sans+Arabic:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&family=Amiri:wght@400;700&display=swap" rel="stylesheet">

  <!-- Enhanced Styles -->
  <link rel="stylesheet" href="styles.css">

  <!-- External Scripts -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // Enhanced Tailwind CSS configuration with RTL support and advanced features
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          fontFamily: {
            'sans': ['Noto Sans Arabic', 'Inter', 'system-ui', 'sans-serif'],
            'arabic': ['Noto Sans Arabic', 'Amiri', 'Tahoma', 'Arial', 'sans-serif'],
            'mono': ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace']
          },
          colors: {
            'primary': {
              50: '#eff6ff',
              100: '#dbeafe',
              200: '#bfdbfe',
              300: '#93c5fd',
              400: '#60a5fa',
              500: '#3b82f6',
              600: '#2563eb',
              700: '#1d4ed8',
              800: '#1e40af',
              900: '#1e3a8a'
            },
            'accent': {
              50: '#f0f9ff',
              100: '#e0f2fe',
              200: '#bae6fd',
              300: '#7dd3fc',
              400: '#38bdf8',
              500: '#0ea5e9',
              600: '#0284c7',
              700: '#0369a1',
              800: '#075985',
              900: '#0c4a6e'
            }
          },
          animation: {
            'fade-in': 'fadeIn 0.8s ease-in-out',
            'slide-up': 'slideUp 0.6s ease-out',
            'slide-in-right': 'slideInRight 0.6s ease-out',
            'slide-in-left': 'slideInLeft 0.6s ease-out',
            'scale-in': 'scaleIn 0.5s ease-out',
            'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            'bounce-slow': 'bounce 2s infinite',
            'spin-slow': 'spin 3s linear infinite',
            'float': 'float 3s ease-in-out infinite'
          },
          backdropBlur: {
            xs: '2px',
          }
        }
      }
    }

    // PDF.js worker configuration
    window.pdfjsWorkerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
  </script>

  <!-- External Libraries -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js" defer></script>
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js" defer></script>

<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.1.0"
  }
}
</script>
</head>
<body class="bg-slate-900 text-slate-100">
  <!-- Enhanced Loading Screen -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <h2 class="text-2xl font-bold text-white mb-4 animate-pulse">
        ⚡ محادثة الكتب التفاعلية بالذكاء الاصطناعي
      </h2>
      <p class="text-lg text-blue-200 mb-6">
        مع مستكشف PDF Flash المتقدم ودعم TTS شامل
      </p>
      <div class="flex justify-center space-x-2 rtl:space-x-reverse">
        <div class="w-3 h-3 bg-blue-400 rounded-full animate-bounce bounce-delay-0"></div>
        <div class="w-3 h-3 bg-blue-400 rounded-full animate-bounce bounce-delay-150"></div>
        <div class="w-3 h-3 bg-blue-400 rounded-full animate-bounce bounce-delay-300"></div>
      </div>
      <p class="text-sm text-slate-300 mt-4">
        الإصدار 1.2.1 - تحليل ذكي للكتب مع دعم عربي كامل وقراءة صوتية متقدمة
      </p>
    </div>
  </div>

  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>

  <script>
    // Enhanced loading screen with fade out
    document.addEventListener('DOMContentLoaded', function() {
      const loadingScreen = document.getElementById('loading-screen');

      // Hide loading screen after React app loads
      setTimeout(() => {
        if (loadingScreen) {
          loadingScreen.classList.add('fade-out');
          setTimeout(() => {
            loadingScreen.style.display = 'none';
          }, 800);
        }
      }, 2000);
    });

    // Enhanced error handling
    window.addEventListener('error', function(e) {
      console.error('Global error:', e.error);
    });

    window.addEventListener('unhandledrejection', function(e) {
      console.error('Unhandled promise rejection:', e.reason);
    });

    // Initialize Mermaid with enhanced settings
    if (typeof mermaid !== 'undefined') {
      mermaid.initialize({
        startOnLoad: true,
        theme: 'dark',
        themeVariables: {
          primaryColor: '#0ea5e9',
          primaryTextColor: '#f1f5f9',
          primaryBorderColor: '#0284c7',
          lineColor: '#38bdf8',
          secondaryColor: '#1e293b',
          tertiaryColor: '#334155'
        }
      });
    }
  </script>
</body>
</html>
