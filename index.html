<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Interactive Textbook</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // pdf.js worker configuration
    window.pdfjsWorkerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
  </script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
  <style>
    /* Ensure SVGs generated by Me<PERSON> are responsive and styled correctly */
    .mermaid svg {
      max-width: 100%;
      height: auto;
    }

    /* Page transition styles for BookViewer */
    /* The parent of .page-content needs perspective, e.g., style={{ perspective: '1500px' }} */
    .page-content {
      width: 100%;
      height: 100%;
      position: absolute; 
      top: 0; left: 0;
      transition: transform 0.6s ease-in-out;
      /* transform-style: preserve-3d; */ /* Not strictly needed for single-face flip if backface is hidden */
      backface-visibility: hidden; /* Important for 3D-like flip */
    }

    /* Stable, visible state */
    .page-enter {
      transform: rotateY(0deg);
    }

    /* Flipping to NEXT page (current page flips away to the left) */
    .page-exit-left {
      transform-origin: left center;
      transform: rotateY(-180deg);
    }

    /* New page (for NEXT) starts flipped and rotates in from the right (appearing from behind) */
    .page-enter-from-right {
      transform-origin: left center; 
      transform: rotateY(180deg); 
    }

    /* Flipping to PREVIOUS page (current page flips away to the right) */
    .page-exit-right {
      transform-origin: right center;
      transform: rotateY(180deg);
    }

    /* New page (for PREVIOUS) starts flipped and rotates in from the left */
    .page-enter-from-left {
      transform-origin: right center;
      transform: rotateY(-180deg); 
    }
    
  </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.1.0"
  }
}
</script>
</head>
<body class="bg-slate-900 text-slate-100">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
