{"name": "ai-interactive-book-chat", "description": "AI-powered interactive book reading experience with voice chat and presentations", "version": "1.0.0", "private": true, "type": "module", "keywords": ["ai", "book", "chat", "education", "interactive", "voice", "presentations"], "author": "AI Interactive Book Chat Team", "license": "MIT", "homepage": ".", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview --host", "type-check": "tsc --noEmit", "lint": "echo '<PERSON><PERSON> not configured yet'", "test": "echo 'Tests not configured yet'"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@google/genai": "^1.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "typescript": "~5.7.2", "vite": "^6.2.0"}, "engines": {"node": ">=18.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}