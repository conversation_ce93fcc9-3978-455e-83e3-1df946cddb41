{"name": "ai-interactive-book-chat", "displayName": "AI Interactive Book Chat", "description": "Transform your reading experience with AI-powered book analysis, topic extraction, interactive presentations, and voice chat features", "version": "1.0.0", "private": true, "type": "module", "keywords": ["ai", "book-analysis", "interactive-reading", "education", "voice-chat", "presentations", "pdf-reader", "document-analysis", "gemini-ai", "react", "typescript"], "author": {"name": "AI Interactive Book Chat Team", "email": "<EMAIL>", "url": "https://ai-book-chat.app"}, "license": "MIT", "homepage": "https://ai-book-chat.app", "repository": {"type": "git", "url": "https://github.com/ai-book-chat/ai-interactive-book-chat.git"}, "bugs": {"url": "https://github.com/ai-book-chat/ai-interactive-book-chat/issues"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview --host", "type-check": "tsc --noEmit", "lint": "echo '<PERSON><PERSON> not configured yet'", "test": "echo 'Tests not configured yet'", "clean": "rm -rf dist node_modules/.vite", "analyze": "vite build --mode analyze"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@google/genai": "^1.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vite-plugin-pwa": "^0.20.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}