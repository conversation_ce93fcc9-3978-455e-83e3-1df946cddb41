{"name": "ai-interactive-book-chat", "displayName": "AI Interactive Book Chat", "description": "Revolutionary AI-powered book analysis platform. Upload PDF, DOCX, or TXT files and transform your reading with intelligent topic extraction, interactive presentations, voice chat, and visual diagrams. Last updated January 2025.", "version": "1.0.1", "private": true, "type": "module", "lastUpdated": "2025-01-01", "keywords": ["ai", "book-analysis", "interactive-reading", "education-technology", "voice-chat", "presentations", "pdf-reader", "document-analysis", "gemini-ai", "react", "typescript", "pwa", "offline-first", "accessibility", "2025"], "author": {"name": "AI Interactive Book Chat Team", "email": "<EMAIL>", "url": "https://ai-book-chat.app"}, "license": "MIT", "homepage": "https://ai-book-chat.app", "repository": {"type": "git", "url": "https://github.com/ai-book-chat/ai-interactive-book-chat.git"}, "bugs": {"url": "https://github.com/ai-book-chat/ai-interactive-book-chat/issues"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "vite --host --port 5173", "build": "vite build", "preview": "vite preview --host --port 4173", "type-check": "tsc --noEmit", "lint": "echo 'ESLint configuration coming soon'", "test": "echo 'Test suite coming soon'", "clean": "rm -rf dist node_modules/.vite .vite", "analyze": "vite build --mode analyze", "start": "npm run dev", "serve": "npm run preview", "update-deps": "npm update && npm audit fix"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@google/genai": "^1.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "typescript": "~5.7.2", "vite": "^5.4.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11", "not op_mini all"], "pwa": {"enabled": true, "workbox": {"globPatterns": ["**/*.{js,css,html,ico,png,svg,woff2}"]}}, "funding": {"type": "github", "url": "https://github.com/sponsors/ai-book-chat"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}