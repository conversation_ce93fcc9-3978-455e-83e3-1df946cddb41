# Icon Placeholder Files

This directory should contain the following icon files for the PWA:

## Required Icons

### Favicon
- `favicon.svg` ✅ (already exists)
- `favicon-16x16.png` (16x16 pixels)
- `favicon-32x32.png` (32x32 pixels)

### Apple Touch Icons
- `apple-touch-icon.png` (180x180 pixels)

### PWA Icons
- `icon-192.png` (192x192 pixels)
- `icon-512.png` (512x512 pixels)
- `icon-upload.png` (96x96 pixels)

### Microsoft Tiles
- `mstile-150x150.png` (150x150 pixels)

### Social Media
- `og-image.png` (1200x630 pixels for Open Graph)
- `twitter-image.png` (1200x600 pixels for Twitter Cards)

### Screenshots for PWA
- `screenshot-wide.png` (1280x720 pixels)
- `screenshot-narrow.png` (640x1136 pixels)

## How to Generate Icons

You can use online tools like:
- [Favicon.io](https://favicon.io/)
- [RealFaviconGenerator](https://realfavicongenerator.net/)
- [PWA Builder](https://www.pwabuilder.com/)

Or create them manually with design tools like:
- Figma
- Adobe Illustrator
- Canva
- GIMP

## Design Guidelines

- Use the app's color scheme (sky blue #0ea5e9)
- Include a book or AI-related icon
- Ensure good contrast and readability at small sizes
- Follow platform-specific guidelines for each icon type

## Temporary Solution

Until proper icons are created, you can use placeholder services:
- `https://via.placeholder.com/192x192/0ea5e9/ffffff?text=AI`
- `https://picsum.photos/192/192`
