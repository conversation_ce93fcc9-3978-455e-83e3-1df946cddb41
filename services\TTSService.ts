// Advanced Text-to-Speech Service with Arabic and English Support
// Version 1.2.1 - Enhanced TTS with Language Detection

export interface TTSVoice {
  voiceURI: string;
  name: string;
  lang: string;
  localService: boolean;
  default: boolean;
}

export interface TTSSettings {
  rate: number;
  pitch: number;
  volume: number;
  voice?: TTSVoice;
  language: 'ar' | 'en' | 'auto';
}

export interface TTSEventCallbacks {
  onStart?: () => void;
  onEnd?: () => void;
  onPause?: () => void;
  onResume?: () => void;
  onError?: (error: string) => void;
  onBoundary?: (event: SpeechSynthesisEvent) => void;
}

class TTSService {
  private utterance: SpeechSynthesisUtterance | null = null;
  private isInitialized = false;
  private availableVoices: SpeechSynthesisVoice[] = [];
  private callbacks: TTSEventCallbacks = {};
  private currentSettings: TTSSettings = {
    rate: 1,
    pitch: 1,
    volume: 1,
    language: 'auto'
  };

  constructor() {
    this.initialize();
  }

  // Initialize TTS service
  private initialize(): void {
    if (!('speechSynthesis' in window)) {
      console.warn('Speech Synthesis not supported in this browser');
      return;
    }

    this.loadVoices();
    speechSynthesis.addEventListener('voiceschanged', () => {
      this.loadVoices();
    });

    this.isInitialized = true;
  }

  // Load available voices
  private loadVoices(): void {
    this.availableVoices = speechSynthesis.getVoices();
  }

  // Detect if text is Arabic
  public isArabicText(text: string): boolean {
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
    const totalChars = text.replace(/\s/g, '').length;
    
    if (totalChars === 0) return false;
    return arabicChars ? (arabicChars.length / totalChars) > 0.3 : false;
  }

  // Get voices for specific language
  public getVoicesForLanguage(language: 'ar' | 'en'): SpeechSynthesisVoice[] {
    return this.availableVoices.filter(voice => 
      voice.lang.startsWith(language)
    );
  }

  // Get best voice for language
  public getBestVoice(language: 'ar' | 'en'): SpeechSynthesisVoice | null {
    const voices = this.getVoicesForLanguage(language);
    
    if (language === 'ar') {
      // Prefer Arabic voices in order of quality
      const preferredArabicVoices = [
        'ar-SA', // Saudi Arabia (Modern Standard Arabic)
        'ar-EG', // Egypt
        'ar-AE', // UAE
        'ar-JO', // Jordan
        'ar-LB', // Lebanon
        'ar-MA', // Morocco
        'ar-TN', // Tunisia
        'ar-DZ', // Algeria
        'ar-IQ', // Iraq
        'ar-SY', // Syria
        'ar-YE', // Yemen
        'ar-KW', // Kuwait
        'ar-QA', // Qatar
        'ar-BH', // Bahrain
        'ar-OM'  // Oman
      ];

      for (const voiceLang of preferredArabicVoices) {
        const voice = voices.find(v => v.lang === voiceLang);
        if (voice) return voice;
      }

      // Fallback to any Arabic voice
      return voices.find(v => v.lang.startsWith('ar')) || null;
    } else {
      // Prefer English voices in order of quality
      const preferredEnglishVoices = [
        'en-US', // US English
        'en-GB', // British English
        'en-AU', // Australian English
        'en-CA', // Canadian English
        'en-IN', // Indian English
        'en-IE', // Irish English
        'en-ZA', // South African English
        'en-NZ'  // New Zealand English
      ];

      for (const voiceLang of preferredEnglishVoices) {
        const voice = voices.find(v => v.lang === voiceLang);
        if (voice) return voice;
      }

      // Fallback to any English voice
      return voices.find(v => v.lang.startsWith('en')) || null;
    }
  }

  // Detect language from text
  public detectLanguage(text: string): 'ar' | 'en' {
    return this.isArabicText(text) ? 'ar' : 'en';
  }

  // Set TTS settings
  public setSettings(settings: Partial<TTSSettings>): void {
    this.currentSettings = { ...this.currentSettings, ...settings };
  }

  // Set event callbacks
  public setCallbacks(callbacks: TTSEventCallbacks): void {
    this.callbacks = callbacks;
  }

  // Create speech utterance
  private createUtterance(text: string): SpeechSynthesisUtterance {
    const utterance = new SpeechSynthesisUtterance(text);
    
    // Detect language if auto
    const language = this.currentSettings.language === 'auto' 
      ? this.detectLanguage(text) 
      : this.currentSettings.language;

    // Set voice
    const voice = this.currentSettings.voice || this.getBestVoice(language);
    if (voice) {
      utterance.voice = voice;
    }

    // Set language
    utterance.lang = language === 'ar' ? 'ar-SA' : 'en-US';

    // Set speech parameters
    utterance.rate = this.currentSettings.rate;
    utterance.pitch = this.currentSettings.pitch;
    utterance.volume = this.currentSettings.volume;

    // Set event handlers
    utterance.onstart = () => {
      this.callbacks.onStart?.();
    };

    utterance.onend = () => {
      this.callbacks.onEnd?.();
    };

    utterance.onerror = (event) => {
      const errorMessage = language === 'ar' 
        ? `خطأ في القراءة الصوتية: ${event.error}`
        : `TTS Error: ${event.error}`;
      this.callbacks.onError?.(errorMessage);
    };

    utterance.onboundary = (event) => {
      this.callbacks.onBoundary?.(event);
    };

    return utterance;
  }

  // Speak text
  public speak(text: string): boolean {
    if (!this.isInitialized || !text.trim()) {
      return false;
    }

    // Stop any current speech
    this.stop();

    try {
      this.utterance = this.createUtterance(text);
      speechSynthesis.speak(this.utterance);
      return true;
    } catch (error) {
      console.error('TTS Speak Error:', error);
      this.callbacks.onError?.(`Failed to start speech: ${error}`);
      return false;
    }
  }

  // Pause speech
  public pause(): void {
    if (speechSynthesis.speaking && !speechSynthesis.paused) {
      speechSynthesis.pause();
      this.callbacks.onPause?.();
    }
  }

  // Resume speech
  public resume(): void {
    if (speechSynthesis.paused) {
      speechSynthesis.resume();
      this.callbacks.onResume?.();
    }
  }

  // Stop speech
  public stop(): void {
    speechSynthesis.cancel();
    this.utterance = null;
  }

  // Check if TTS is supported
  public isSupported(): boolean {
    return 'speechSynthesis' in window;
  }

  // Check if currently speaking
  public isSpeaking(): boolean {
    return speechSynthesis.speaking;
  }

  // Check if currently paused
  public isPaused(): boolean {
    return speechSynthesis.paused;
  }

  // Get all available voices
  public getAllVoices(): SpeechSynthesisVoice[] {
    return this.availableVoices;
  }

  // Get current settings
  public getSettings(): TTSSettings {
    return { ...this.currentSettings };
  }

  // Quick speak with auto-detection
  public quickSpeak(text: string, rate: number = 1): boolean {
    const language = this.detectLanguage(text);
    const voice = this.getBestVoice(language);
    
    this.setSettings({
      rate,
      voice: voice || undefined,
      language
    });

    return this.speak(text);
  }

  // Speak with custom voice
  public speakWithVoice(text: string, voiceURI: string): boolean {
    const voice = this.availableVoices.find(v => v.voiceURI === voiceURI);
    if (!voice) return false;

    this.setSettings({ voice });
    return this.speak(text);
  }

  // Get voice statistics
  public getVoiceStats(): { arabic: number; english: number; total: number } {
    const arabicVoices = this.getVoicesForLanguage('ar').length;
    const englishVoices = this.getVoicesForLanguage('en').length;
    
    return {
      arabic: arabicVoices,
      english: englishVoices,
      total: this.availableVoices.length
    };
  }

  // Test voice with sample text
  public testVoice(voiceURI: string): boolean {
    const voice = this.availableVoices.find(v => v.voiceURI === voiceURI);
    if (!voice) return false;

    const isArabic = voice.lang.startsWith('ar');
    const sampleText = isArabic 
      ? 'مرحباً، هذا اختبار للصوت العربي. كيف تبدو جودة الصوت؟'
      : 'Hello, this is a voice test. How does the voice quality sound?';

    return this.speakWithVoice(sampleText, voiceURI);
  }
}

// Create singleton instance
export const ttsService = new TTSService();

// Export default
export default ttsService;
