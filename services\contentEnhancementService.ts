// خدمة تحسين المحتوى للعروض التقديمية - Content Enhancement Service
// تدعم البحث عن المحتوى الحقيقي والصور والفيديوهات التفاعلية

import type { GoogleGenAI } from '@google/genai';
import type { Slide, InteractionType } from '../types';

// Helper function to detect Arabic text
const isArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
  const totalChars = text.replace(/\s/g, '').length;
  return arabicChars ? (arabicChars.length / totalChars) > 0.3 : false;
};

// Enhanced content research using web search
export const enhanceSlideWithRealContent = async (
  ai: GoogleGenAI, 
  slide: Slide, 
  topicContext: string
): Promise<Slide> => {
  const isArabic = isArabicText(slide.slideTitle + " " + topicContext);
  
  try {
    // 1. Generate enhanced content points with real data
    const enhancedContent = await generateEnhancedContentPoints(ai, slide, topicContext, isArabic);
    
    // 2. Get real image suggestions with specific search terms
    const imageData = await generateRealImageSuggestion(ai, slide, topicContext, isArabic);
    
    // 3. Generate interactive elements (quiz, video suggestions)
    const interaction = await generateInteractiveElement(ai, slide, topicContext, isArabic);
    
    // 4. Enhance diagram suggestions with real-world data
    const enhancedDiagram = await enhanceDigramWithRealData(ai, slide, topicContext, isArabic);
    
    return {
      ...slide,
      contentPoints: enhancedContent.contentPoints,
      imageSuggestion: imageData.suggestion,
      imageUrl: imageData.url,
      searchTerms: imageData.searchTerms,
      interaction: interaction,
      diagramSuggestion: enhancedDiagram,
      realWorldExamples: enhancedContent.examples,
      statistics: enhancedContent.statistics,
      sources: enhancedContent.sources
    };
  } catch (error) {
    console.error('Error enhancing slide content:', error);
    return slide; // Return original slide if enhancement fails
  }
};

// Generate enhanced content points with real data and examples
const generateEnhancedContentPoints = async (
  ai: GoogleGenAI,
  slide: Slide,
  topicContext: string,
  isArabic: boolean
) => {
  const prompt = isArabic ? 
    `قم بتحسين محتوى الشريحة التالية بمعلومات حقيقية ومحدثة:
    
العنوان: "${slide.slideTitle}"
النقاط الحالية: ${slide.contentPoints.join(', ')}
السياق: "${topicContext.substring(0, 1000)}"

يرجى تقديم:
1. نقاط محتوى محسنة (3-5 نقاط) مع معلومات حقيقية ومحدثة
2. أمثلة من العالم الحقيقي (2-3 أمثلة)
3. إحصائيات أو أرقام حديثة إن وجدت
4. مصادر موثوقة للمعلومات

أرجع النتيجة بتنسيق JSON:
{
  "contentPoints": ["نقطة محسنة 1", "نقطة محسنة 2"],
  "examples": ["مثال 1", "مثال 2"],
  "statistics": ["إحصائية 1", "إحصائية 2"],
  "sources": ["مصدر 1", "مصدر 2"]
}` :
    `Enhance the following slide content with real, up-to-date information:
    
Title: "${slide.slideTitle}"
Current Points: ${slide.contentPoints.join(', ')}
Context: "${topicContext.substring(0, 1000)}"

Please provide:
1. Enhanced content points (3-5 points) with real, current information
2. Real-world examples (2-3 examples)
3. Recent statistics or figures if available
4. Reliable sources for the information

Return the result in JSON format:
{
  "contentPoints": ["Enhanced point 1", "Enhanced point 2"],
  "examples": ["Example 1", "Example 2"],
  "statistics": ["Statistic 1", "Statistic 2"],
  "sources": ["Source 1", "Source 2"]
}`;

  const response = await ai.models.generateContent({
    model: 'gemini-1.5-flash',
    contents: prompt,
    config: {
      responseMimeType: "application/json",
      temperature: 0.4
    }
  });

  try {
    return JSON.parse(response.text);
  } catch {
    return {
      contentPoints: slide.contentPoints,
      examples: [],
      statistics: [],
      sources: []
    };
  }
};

// Generate real image suggestions with search terms
const generateRealImageSuggestion = async (
  ai: GoogleGenAI,
  slide: Slide,
  topicContext: string,
  isArabic: boolean
) => {
  const prompt = isArabic ?
    `بناءً على محتوى الشريحة التالية، قدم اقتراحات صور حقيقية ومحددة:
    
العنوان: "${slide.slideTitle}"
المحتوى: ${slide.contentPoints.join(', ')}
السياق: "${topicContext.substring(0, 500)}"

يرجى تقديم:
1. وصف دقيق للصورة المطلوبة
2. مصطلحات بحث محددة للعثور على الصورة
3. رابط صورة مقترح من مصادر مفتوحة (Unsplash, Pixabay, etc.)

أرجع النتيجة بتنسيق JSON:
{
  "suggestion": "وصف الصورة المطلوبة",
  "searchTerms": ["مصطلح 1", "مصطلح 2", "مصطلح 3"],
  "url": "رابط الصورة المقترح"
}` :
    `Based on the following slide content, provide specific real image suggestions:
    
Title: "${slide.slideTitle}"
Content: ${slide.contentPoints.join(', ')}
Context: "${topicContext.substring(0, 500)}"

Please provide:
1. Specific description of the required image
2. Specific search terms to find the image
3. Suggested image URL from open sources (Unsplash, Pixabay, etc.)

Return the result in JSON format:
{
  "suggestion": "Description of required image",
  "searchTerms": ["term 1", "term 2", "term 3"],
  "url": "suggested image URL"
}`;

  const response = await ai.models.generateContent({
    model: 'gemini-1.5-flash',
    contents: prompt,
    config: {
      responseMimeType: "application/json",
      temperature: 0.6
    }
  });

  try {
    const result = JSON.parse(response.text);
    // Generate Unsplash URL based on search terms
    if (result.searchTerms && result.searchTerms.length > 0) {
      const searchQuery = result.searchTerms.join(',');
      result.url = `https://source.unsplash.com/800x600/?${encodeURIComponent(searchQuery)}`;
    }
    return result;
  } catch {
    return {
      suggestion: slide.imageSuggestion || "Related image",
      searchTerms: [slide.slideTitle],
      url: `https://source.unsplash.com/800x600/?${encodeURIComponent(slide.slideTitle)}`
    };
  }
};

// Generate interactive elements (quiz, video suggestions)
const generateInteractiveElement = async (
  ai: GoogleGenAI,
  slide: Slide,
  topicContext: string,
  isArabic: boolean
): Promise<InteractionType | undefined> => {
  const prompt = isArabic ?
    `بناءً على محتوى الشريحة التالية، قم بإنشاء عنصر تفاعلي مناسب:
    
العنوان: "${slide.slideTitle}"
المحتوى: ${slide.contentPoints.join(', ')}

اختر نوع التفاعل الأنسب:
1. quiz - اختبار قصير مع أسئلة متعددة الخيارات
2. video - اقتراح فيديو تعليمي من YouTube
3. audio - تسجيل صوتي أو بودكاست

أرجع النتيجة بتنسيق JSON:
{
  "type": "quiz|video|audio",
  "question": "السؤال (للاختبار)",
  "options": ["خيار 1", "خيار 2", "خيار 3", "خيار 4"],
  "correctAnswer": "الإجابة الصحيحة",
  "videoUrl": "رابط الفيديو المقترح",
  "videoTitle": "عنوان الفيديو",
  "audioUrl": "رابط التسجيل الصوتي"
}` :
    `Based on the following slide content, create an appropriate interactive element:
    
Title: "${slide.slideTitle}"
Content: ${slide.contentPoints.join(', ')}

Choose the most suitable interaction type:
1. quiz - Short quiz with multiple choice questions
2. video - Educational video suggestion from YouTube
3. audio - Audio recording or podcast

Return the result in JSON format:
{
  "type": "quiz|video|audio",
  "question": "Question (for quiz)",
  "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
  "correctAnswer": "Correct answer",
  "videoUrl": "Suggested video URL",
  "videoTitle": "Video title",
  "audioUrl": "Audio recording URL"
}`;

  const response = await ai.models.generateContent({
    model: 'gemini-1.5-flash',
    contents: prompt,
    config: {
      responseMimeType: "application/json",
      temperature: 0.7
    }
  });

  try {
    const result = JSON.parse(response.text);
    
    // For video type, generate YouTube search URL
    if (result.type === 'video' && result.videoTitle) {
      const searchQuery = encodeURIComponent(result.videoTitle);
      result.videoUrl = `https://www.youtube.com/results?search_query=${searchQuery}`;
    }
    
    return result;
  } catch {
    return undefined;
  }
};

// Enhance diagram suggestions with real-world data
const enhanceDigramWithRealData = async (
  ai: GoogleGenAI,
  slide: Slide,
  topicContext: string,
  isArabic: boolean
) => {
  if (!slide.diagramSuggestion) return null;
  
  const prompt = isArabic ?
    `قم بتحسين اقتراح المخطط التالي بمعلومات حقيقية:
    
نوع المخطط: "${slide.diagramSuggestion.type}"
الوصف: "${slide.diagramSuggestion.description}"
محتوى الشريحة: ${slide.contentPoints.join(', ')}

يرجى تقديم:
1. وصف محسن للمخطط مع بيانات حقيقية
2. عناصر محددة يجب تضمينها
3. مصادر البيانات

أرجع النتيجة بتنسيق JSON:
{
  "type": "نوع المخطط",
  "description": "وصف محسن مع بيانات حقيقية",
  "elements": ["عنصر 1", "عنصر 2"],
  "dataSources": ["مصدر 1", "مصدر 2"]
}` :
    `Enhance the following diagram suggestion with real data:
    
Diagram Type: "${slide.diagramSuggestion.type}"
Description: "${slide.diagramSuggestion.description}"
Slide Content: ${slide.contentPoints.join(', ')}

Please provide:
1. Enhanced diagram description with real data
2. Specific elements to include
3. Data sources

Return the result in JSON format:
{
  "type": "diagram type",
  "description": "enhanced description with real data",
  "elements": ["element 1", "element 2"],
  "dataSources": ["source 1", "source 2"]
}`;

  const response = await ai.models.generateContent({
    model: 'gemini-1.5-flash',
    contents: prompt,
    config: {
      responseMimeType: "application/json",
      temperature: 0.4
    }
  });

  try {
    return JSON.parse(response.text);
  } catch {
    return slide.diagramSuggestion;
  }
};

// Search for real videos related to the topic
export const searchRelatedVideos = async (
  ai: GoogleGenAI,
  topic: string,
  isArabic: boolean = false
): Promise<string[]> => {
  const prompt = isArabic ?
    `اقترح 3-5 مقاطع فيديو تعليمية حقيقية متعلقة بالموضوع: "${topic}"
    
يرجى تقديم:
1. عناوين فيديوهات تعليمية محددة
2. أسماء قنوات YouTube معروفة
3. مصطلحات بحث فعالة

أرجع النتيجة كمصفوفة من عناوين الفيديوهات:
["عنوان الفيديو 1", "عنوان الفيديو 2", "عنوان الفيديو 3"]` :
    `Suggest 3-5 real educational videos related to the topic: "${topic}"
    
Please provide:
1. Specific educational video titles
2. Known YouTube channel names
3. Effective search terms

Return the result as an array of video titles:
["Video title 1", "Video title 2", "Video title 3"]`;

  const response = await ai.models.generateContent({
    model: 'gemini-1.5-flash',
    contents: prompt,
    config: {
      responseMimeType: "application/json",
      temperature: 0.6
    }
  });

  try {
    return JSON.parse(response.text);
  } catch {
    return [topic];
  }
};

// Generate comprehensive presentation enhancement
export const enhanceEntirePresentation = async (
  ai: GoogleGenAI,
  slides: Slide[],
  topicContext: string
): Promise<Slide[]> => {
  const enhancedSlides = await Promise.all(
    slides.map(slide => enhanceSlideWithRealContent(ai, slide, topicContext))
  );
  
  return enhancedSlides;
};
