
import { GoogleGenAI, GenerateContentResponse } from '@google/genai';
import type { Topic, Presentation, Slide } from '../types';
import { GEMINI_TEXT_MODEL } from '../constants';

// Helper function to detect Arabic text
const isArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
  const totalChars = text.replace(/\s/g, '').length;
  return arabicChars ? (arabicChars.length / totalChars) > 0.3 : false;
};

// Enhanced helper to parse JSON with better error handling
const parseJsonFromGeminiResponse = <T,>(responseText: string, fallbackData?: T): T => {
  let jsonStr = responseText.trim();

  // Remove markdown code fences if present
  const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
  const match = jsonStr.match(fenceRegex);
  if (match && match[2]) {
    jsonStr = match[2].trim();
  }

  // Try to extract JSON from mixed content
  const jsonMatch = jsonStr.match(/\{[\s\S]*\}/);
  if (jsonMatch) {
    jsonStr = jsonMatch[0];
  }

  try {
    const parsed = JSON.parse(jsonStr) as T;
    return parsed;
  } catch (e) {
    console.error("Failed to parse JSON response:", jsonStr, e);

    // If fallback data is provided, use it
    if (fallbackData) {
      console.warn("Using fallback data due to JSON parsing error");
      return fallbackData;
    }

    throw new Error(`Invalid JSON response from AI: ${(e as Error).message}`);
  }
};

// Validate content length and prepare for AI processing
const prepareContentForAI = (content: string, maxLength: number = 5000): string => {
  if (!content || content.trim().length === 0) {
    throw new Error("Content is empty or invalid");
  }

  const trimmedContent = content.trim();
  if (trimmedContent.length > maxLength) {
    // Take content from the beginning and try to end at a sentence boundary
    let truncated = trimmedContent.substring(0, maxLength);
    const lastSentenceEnd = Math.max(
      truncated.lastIndexOf('.'),
      truncated.lastIndexOf('!'),
      truncated.lastIndexOf('?'),
      truncated.lastIndexOf('。'), // Arabic/Chinese period
      truncated.lastIndexOf('؟'), // Arabic question mark
      truncated.lastIndexOf('!')  // Arabic exclamation
    );

    if (lastSentenceEnd > maxLength * 0.7) {
      truncated = truncated.substring(0, lastSentenceEnd + 1);
    }

    return truncated;
  }

  return trimmedContent;
};


export const extractTopics = async (ai: GoogleGenAI, bookContent: string): Promise<Topic[]> => {
  try {
    // Prepare and validate content
    const preparedContent = prepareContentForAI(bookContent, 5000);
    const isArabic = isArabicText(preparedContent);

    // Create language-appropriate prompt
    const prompt = isArabic ?
      `قم بتحليل محتوى الكتاب التالي واستخرج حتى 5 مواضيع رئيسية. لكل موضوع، قدم عنواناً مختصراً وملخصاً موجزاً (جملة أو جملتان). أرجع النتيجة كمصفوفة JSON من الكائنات، حيث كل كائن يحتوي على 'id' (نص فريد، مثل topic-1)، 'title' (نص)، و 'summary' (نص).

يجب أن تكون جميع النصوص باللغة العربية الفصحى الصحيحة.

المحتوى:
${preparedContent}

مثال على التنسيق المطلوب:
[
  {
    "id": "topic-1",
    "title": "العنوان الأول",
    "summary": "ملخص موجز للموضوع الأول يشرح النقاط الرئيسية."
  }
]` :
      `Analyze the following book content and extract up to 5 main topics. For each topic, provide a concise title and a brief summary (1-2 sentences). Return the result as a JSON array of objects, where each object has 'id' (a unique string, e.g., topic-1), 'title' (string), and 'summary' (string).

Content:
${preparedContent}

Required JSON format:
[
  {
    "id": "topic-1",
    "title": "Topic Title",
    "summary": "Brief summary of the topic explaining key points."
  }
]`;

    console.log(`🔍 Extracting topics from ${isArabic ? 'Arabic' : 'English'} content (${preparedContent.length} characters)`);

    const response: GenerateContentResponse = await ai.models.generateContent({
      model: GEMINI_TEXT_MODEL,
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        temperature: 0.3, // Lower temperature for more deterministic topic extraction
        maxOutputTokens: 2048,
      },
    });

    if (!response.text || response.text.trim().length === 0) {
      throw new Error("Empty response from AI service");
    }

    // Fallback data in case of parsing errors
    const fallbackTopics: Topic[] = [
      {
        id: "topic-1",
        title: isArabic ? "الموضوع الرئيسي" : "Main Topic",
        summary: isArabic ?
          "تم استخراج هذا الموضوع من المحتوى المقدم. يرجى المحاولة مرة أخرى للحصول على تحليل أكثر تفصيلاً." :
          "This topic was extracted from the provided content. Please try again for more detailed analysis."
      }
    ];

    const topics = parseJsonFromGeminiResponse<Topic[]>(response.text, fallbackTopics);

    // Validate and ensure proper structure
    if (!Array.isArray(topics) || topics.length === 0) {
      console.warn("Invalid topics structure, using fallback");
      return fallbackTopics;
    }

    // Ensure IDs are unique and all required fields are present
    const validatedTopics = topics
      .filter(topic => topic && typeof topic === 'object' && topic.title && topic.summary)
      .map((topic, index) => ({
        id: topic.id || `topic-${index + 1}`,
        title: topic.title.trim(),
        summary: topic.summary.trim()
      }))
      .slice(0, 5); // Limit to 5 topics

    if (validatedTopics.length === 0) {
      console.warn("No valid topics found, using fallback");
      return fallbackTopics;
    }

    console.log(`✅ Successfully extracted ${validatedTopics.length} topics`);
    return validatedTopics;

  } catch (error) {
    console.error("Error in extractTopics:", error);

    // Determine language for fallback
    const isArabic = isArabicText(bookContent);

    // Return fallback topics with error handling
    const fallbackTopics: Topic[] = [
      {
        id: "topic-error-1",
        title: isArabic ? "خطأ في استخراج المواضيع" : "Topic Extraction Error",
        summary: isArabic ?
          `حدث خطأ أثناء تحليل المحتوى: ${error instanceof Error ? error.message : 'خطأ غير معروف'}. يرجى المحاولة مرة أخرى.` :
          `Error occurred during content analysis: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`
      }
    ];

    return fallbackTopics;
  }
};


export const generatePresentationOutline = async (ai: GoogleGenAI, topicTitle: string, topicContext: string): Promise<Presentation> => {
  try {
    // Prepare content and detect language
    const preparedContext = prepareContentForAI(topicContext, 3000);
    const isArabic = isArabicText(topicTitle + " " + preparedContext);

    // Create language-appropriate prompt
    const prompt = isArabic ?
      `أنشئ مخطط عرض تقديمي للموضوع: "${topicTitle}"

السياق: ${preparedContext}

يجب أن يحتوي العرض التقديمي على عنوان رئيسي و 3-5 شرائح.
لكل شريحة، قدم:
1. 'slideTitle': عنوان مختصر للشريحة
2. 'contentPoints': مصفوفة من 2-4 نقاط رئيسية (نصوص)
3. 'imageSuggestion': (اختياري) وصف موجز لصورة ذات صلة إذا كانت مناسبة. اجعلها null إذا لم تكن الصورة مناسبة
4. 'diagramSuggestion': (اختياري) كائن يحتوي على 'type' و 'description' للمخطط إذا كان مفيداً. اجعله null إذا لم يكن المخطط مناسباً

يجب أن تكون جميع النصوص باللغة العربية الفصحى الصحيحة.

أرجع النتيجة ككائن JSON واحد بالمفاتيح: 'title' (نص للعرض التقديمي الكامل) و 'slides' (مصفوفة من كائنات الشرائح).
مثال للشريحة: { "slideTitle": "مقدمة للموضوع", "contentPoints": ["النقطة الأولى", "النقطة الثانية"], "imageSuggestion": "تمثيل مجرد للموضوع", "diagramSuggestion": null }` :
      `Generate a presentation outline for the topic "${topicTitle}" based on the following context:
Context: "${preparedContext}"

The presentation should have a main title and 3-5 slides.
For each slide, provide:
1. 'slideTitle': A concise title for the slide
2. 'contentPoints': An array of 2-4 key bullet points (strings)
3. 'imageSuggestion': (Optional) A brief description for a relevant image if applicable (e.g., "A brain with interconnected nodes for AI"). Set to null if no image is suitable
4. 'diagramSuggestion': (Optional) An object with 'type' (e.g., "flowchart", "mindmap", "sequence") and 'description' (what the diagram should illustrate) if a diagram would be beneficial. Set to null if no diagram is suitable

Return the result as a single JSON object with keys: 'title' (string, for the overall presentation) and 'slides' (an array of slide objects as described above).
Example for a slide: { "slideTitle": "Introduction to Topic", "contentPoints": ["Point 1", "Point 2"], "imageSuggestion": "Abstract representation of the topic", "diagramSuggestion": null }`;

    console.log(`🎯 Generating presentation outline for "${topicTitle}" in ${isArabic ? 'Arabic' : 'English'}`);

    const response: GenerateContentResponse = await ai.models.generateContent({
      model: GEMINI_TEXT_MODEL,
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        temperature: 0.5,
        maxOutputTokens: 4096,
      },
    });

    if (!response.text || response.text.trim().length === 0) {
      throw new Error("Empty response from AI service");
    }

    // Fallback presentation in case of parsing errors
    const fallbackPresentation: Presentation = {
      title: isArabic ? `عرض تقديمي: ${topicTitle}` : `Presentation: ${topicTitle}`,
      slides: [
        {
          slideTitle: isArabic ? "مقدمة" : "Introduction",
          contentPoints: [
            isArabic ? "مرحباً بكم في هذا العرض التقديمي" : "Welcome to this presentation",
            isArabic ? `سنتناول موضوع: ${topicTitle}` : `We will cover the topic: ${topicTitle}`,
            isArabic ? "دعونا نبدأ بالاستكشاف" : "Let's begin exploring"
          ],
          imageSuggestion: isArabic ?
            "صورة ترحيبية احترافية للعرض التقديمي" :
            "Professional welcome image for presentation",
          diagramSuggestion: null
        },
        {
          slideTitle: isArabic ? "النقاط الرئيسية" : "Key Points",
          contentPoints: [
            isArabic ? "النقطة الأولى من الموضوع" : "First key point of the topic",
            isArabic ? "النقطة الثانية المهمة" : "Second important point",
            isArabic ? "النقطة الثالثة الأساسية" : "Third fundamental point"
          ],
          imageSuggestion: null,
          diagramSuggestion: {
            type: "flowchart",
            description: isArabic ?
              "مخطط انسيابي يوضح النقاط الرئيسية" :
              "Flowchart showing key points"
          }
        },
        {
          slideTitle: isArabic ? "الخلاصة" : "Conclusion",
          contentPoints: [
            isArabic ? "ملخص النقاط المهمة" : "Summary of important points",
            isArabic ? "الاستنتاجات الرئيسية" : "Key conclusions",
            isArabic ? "شكراً لكم على المتابعة" : "Thank you for your attention"
          ],
          imageSuggestion: isArabic ?
            "صورة ختامية تعبر عن النجاح والإنجاز" :
            "Concluding image expressing success and achievement",
          diagramSuggestion: null
        }
      ]
    };

    const presentation = parseJsonFromGeminiResponse<Presentation>(response.text, fallbackPresentation);

    // Validate presentation structure
    if (!presentation || !presentation.title || !Array.isArray(presentation.slides)) {
      console.warn("Invalid presentation structure, using fallback");
      return fallbackPresentation;
    }

    // Validate and clean slides
    const validatedSlides = presentation.slides
      .filter(slide => slide && slide.slideTitle && Array.isArray(slide.contentPoints))
      .map(slide => ({
        slideTitle: slide.slideTitle.trim(),
        contentPoints: slide.contentPoints
          .filter(point => typeof point === 'string' && point.trim().length > 0)
          .map(point => point.trim())
          .slice(0, 5), // Limit to 5 points per slide
        imageSuggestion: slide.imageSuggestion?.trim() || null,
        diagramSuggestion: slide.diagramSuggestion || null
      }))
      .filter(slide => slide.contentPoints.length > 0)
      .slice(0, 7); // Limit to 7 slides

    if (validatedSlides.length === 0) {
      console.warn("No valid slides found, using fallback");
      return fallbackPresentation;
    }

    const validatedPresentation: Presentation = {
      title: presentation.title.trim(),
      slides: validatedSlides
    };

    console.log(`✅ Successfully generated presentation with ${validatedSlides.length} slides`);
    return validatedPresentation;

  } catch (error) {
    console.error("Error in generatePresentationOutline:", error);

    // Determine language for fallback
    const isArabic = isArabicText(topicTitle + " " + topicContext);

    // Return fallback presentation with error handling
    const errorPresentation: Presentation = {
      title: isArabic ?
        `خطأ في إنشاء العرض التقديمي: ${topicTitle}` :
        `Presentation Generation Error: ${topicTitle}`,
      slides: [
        {
          slideTitle: isArabic ? "خطأ في الإنشاء" : "Generation Error",
          contentPoints: [
            isArabic ?
              `حدث خطأ أثناء إنشاء العرض التقديمي: ${error instanceof Error ? error.message : 'خطأ غير معروف'}` :
              `Error occurred during presentation generation: ${error instanceof Error ? error.message : 'Unknown error'}`,
            isArabic ? "يرجى المحاولة مرة أخرى" : "Please try again",
            isArabic ? "تأكد من اتصال الإنترنت" : "Check your internet connection"
          ],
          imageSuggestion: null,
          diagramSuggestion: null
        }
      ]
    };

    return errorPresentation;
  }
};

export const generateImageDescriptionForTopic = async (ai: GoogleGenAI, slideContext: string): Promise<string> => {
  try {
    const preparedContext = prepareContentForAI(slideContext, 500);
    const isArabic = isArabicText(preparedContext);

    const prompt = isArabic ?
      `بناءً على سياق الشريحة: "${preparedContext}"، قدم وصفاً بصرياً مختصراً (بحد أقصى 15 كلمة) مناسب لنموذج إنشاء الصور بالذكاء الاصطناعي لإنشاء صورة احترافية ذات صلة. ركز على المفاهيم الأساسية أو الاستعارات. مثال: "شبكة عصبية متوهجة تربط نقاط بيانات متنوعة."

يجب أن يكون الوصف باللغة الإنجليزية لتوافق مع نماذج إنشاء الصور.` :
      `Based on the slide context: "${preparedContext}", provide a concise, visually descriptive prompt (max 15 words) suitable for an AI image generation model to create a relevant, professional image. Focus on key concepts or metaphors. Example: "Glowing neural network connecting diverse data points."`;

    const response: GenerateContentResponse = await ai.models.generateContent({
      model: GEMINI_TEXT_MODEL,
      contents: prompt,
      config: {
        temperature: 0.7,
        maxOutputTokens: 100
      }
    });

    if (!response.text || response.text.trim().length === 0) {
      return "Professional abstract illustration representing the topic";
    }

    return response.text.trim();

  } catch (error) {
    console.error("Error in generateImageDescriptionForTopic:", error);
    return "Professional abstract illustration representing the topic";
  }
};

export const generateMermaidSyntaxForTopic = async (ai: GoogleGenAI, diagramType: string, diagramDescription: string): Promise<string> => {
  try {
    const preparedDescription = prepareContentForAI(diagramDescription, 500);
    const isArabic = isArabicText(preparedDescription);

    const prompt = isArabic ?
      `أنشئ صيغة MermaidJS لمخطط "${diagramType}" يوضح التالي: "${preparedDescription}".

أرجع صيغة Mermaid فقط، بدءاً من نوع المخطط (مثل "graph TD", "flowchart LR", "sequenceDiagram", إلخ).
تأكد من أن الصيغة صحيحة وتستخدم تسميات واضحة ومختصرة.

يجب أن تكون التسميات باللغة الإنجليزية لضمان التوافق مع Mermaid.

مثال لمخطط انسيابي:
graph TD
    A[Start] --> B[Process]
    B --> C[Decision]
    C -->|Yes| D[Action 1]
    C -->|No| E[Action 2]` :
      `Generate MermaidJS syntax for a "${diagramType}" diagram that illustrates the following: "${preparedDescription}".

Return only the Mermaid syntax, starting with the diagram type (e.g., "graph TD", "flowchart LR", "sequenceDiagram", etc.).
Ensure the syntax is valid and uses clear, concise labels.

Example for a flowchart:
graph TD
    A[Start] --> B[Process]
    B --> C[Decision]
    C -->|Yes| D[Action 1]
    C -->|No| E[Action 2]`;

    const response: GenerateContentResponse = await ai.models.generateContent({
      model: GEMINI_TEXT_MODEL,
      contents: prompt,
      config: {
        temperature: 0.3, // Lower temperature for more consistent syntax
        maxOutputTokens: 1024
      }
    });

    if (!response.text || response.text.trim().length === 0) {
      // Return a simple fallback diagram
      return `graph TD
    A[Start] --> B[Process]
    B --> C[End]`;
    }

    let mermaidSyntax = response.text.trim();

    // Extract code if wrapped in markdown, otherwise use as is
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = mermaidSyntax.match(fenceRegex);
    if (match && match[2]) {
      mermaidSyntax = match[2].trim();
    }

    // Basic validation - ensure it starts with a valid Mermaid diagram type
    const validStarters = ['graph', 'flowchart', 'sequenceDiagram', 'classDiagram', 'stateDiagram', 'erDiagram', 'journey', 'gantt', 'pie', 'gitgraph'];
    const startsWithValid = validStarters.some(starter => mermaidSyntax.toLowerCase().startsWith(starter.toLowerCase()));

    if (!startsWithValid) {
      console.warn("Generated Mermaid syntax doesn't start with valid diagram type, using fallback");
      return `graph TD
    A[${diagramType}] --> B[Process]
    B --> C[Result]`;
    }

    return mermaidSyntax;

  } catch (error) {
    console.error("Error in generateMermaidSyntaxForTopic:", error);
    return `graph TD
    A[Start] --> B[Process]
    B --> C[End]`;
  }
};
