
import { GoogleGenAI, GenerateContentResponse } from '@google/genai';
import type { Topic, Presentation, Slide } from '../types';
import { GEMINI_TEXT_MODEL } from '../constants';

// Helper to parse JSON, handling potential markdown fences
const parseJsonFromGeminiResponse = <T,>(responseText: string): T => {
  let jsonStr = responseText.trim();
  const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
  const match = jsonStr.match(fenceRegex);
  if (match && match[2]) {
    jsonStr = match[2].trim();
  }
  try {
    return JSON.parse(jsonStr) as T;
  } catch (e) {
    console.error("Failed to parse JSON response:", jsonStr, e);
    throw new Error(`Invalid JSON response from AI: ${(e as Error).message}`);
  }
};


// Helper function to detect if text is primarily Arabic
const isArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
  const totalChars = text.replace(/\s/g, '').length;
  return arabicChars ? (arabicChars.length / totalChars) > 0.3 : false;
};

export const extractTopics = async (ai: GoogleGenAI, bookContent: string): Promise<Topic[]> => {
  const isArabic = isArabicText(bookContent);

  const prompt = isArabic ?
    `قم بتحليل محتوى الكتاب التالي (أول 5000 حرف) واستخرج حتى 5 مواضيع رئيسية. لكل موضوع، قدم عنواناً مختصراً وملخصاً موجزاً (جملة أو جملتين). أرجع النتيجة كمصفوفة JSON من الكائنات، حيث كل كائن يحتوي على 'id' (نص فريد، مثل topic-1)، 'title' (نص)، و 'summary' (نص). يجب أن تكون جميع النصوص باللغة العربية الفصحى الصحيحة.

المحتوى:
${bookContent.substring(0, 5000)}

مثال على التنسيق المطلوب:
[
  {
    "id": "topic-1",
    "title": "العنوان الأول",
    "summary": "ملخص موجز للموضوع الأول يوضح النقاط الرئيسية."
  }
]` :
    `Analyze the following book content (first 5000 characters) and extract up to 5 main topics. For each topic, provide a concise title and a brief summary (1-2 sentences). Return the result as a JSON array of objects, where each object has 'id' (a unique string, e.g., topic-1), 'title' (string), and 'summary' (string).

Content:
${bookContent.substring(0, 5000)}

Example format:
[
  {
    "id": "topic-1",
    "title": "First Topic Title",
    "summary": "Brief summary of the first topic explaining key points."
  }
]`;

  const response: GenerateContentResponse = await ai.models.generateContent({
    model: GEMINI_TEXT_MODEL,
    contents: prompt,
    config: {
      responseMimeType: "application/json",
      temperature: 0.3, // Lower temperature for more deterministic topic extraction
    },
  });

  const topics = parseJsonFromGeminiResponse<Topic[]>(response.text);
  // Ensure IDs are unique if not provided by AI
  return topics.map((topic, index) => ({ ...topic, id: topic.id || `topic-${index + 1}` }));
};


export const generatePresentationOutline = async (ai: GoogleGenAI, topicTitle: string, topicContext: string): Promise<Presentation> => {
  const isArabic = isArabicText(topicTitle + " " + topicContext);

  const prompt = isArabic ?
    `قم بإنشاء مخطط عرض تقديمي للموضوع "${topicTitle}" بناءً على السياق التالي:
السياق: "${topicContext.substring(0, 3000)}"

يجب أن يحتوي العرض التقديمي على عنوان رئيسي و 3-5 شرائح.
لكل شريحة، قدم:
1. 'slideTitle': عنوان مختصر للشريحة.
2. 'contentPoints': مصفوفة من 2-4 نقاط رئيسية (نصوص).
3. 'imageSuggestion': (اختياري) وصف موجز لصورة ذات صلة إذا كان ذلك مناسباً (مثل "دماغ مع عقد مترابطة للذكاء الاصطناعي"). اجعلها null إذا لم تكن الصورة مناسبة.
4. 'diagramSuggestion': (اختياري) كائن يحتوي على 'type' (مثل "flowchart", "mindmap", "sequence") و 'description' (ما يجب أن يوضحه المخطط) إذا كان المخطط مفيداً. اجعلها null إذا لم يكن المخطط مناسباً.

أرجع النتيجة كائن JSON واحد يحتوي على مفاتيح: 'title' (نص، للعرض التقديمي الكامل) و 'slides' (مصفوفة من كائنات الشرائح كما هو موضح أعلاه).
يجب أن تكون جميع النصوص باللغة العربية الفصحى الصحيحة.

مثال على شريحة: { "slideTitle": "مقدمة عن الموضوع", "contentPoints": ["النقطة الأولى", "النقطة الثانية"], "imageSuggestion": "تمثيل مجرد للموضوع", "diagramSuggestion": null }` :
    `Generate a presentation outline for the topic "${topicTitle}" based on the following context:
Context: "${topicContext.substring(0, 3000)}"

The presentation should have a main title and 3-5 slides.
For each slide, provide:
1. 'slideTitle': A concise title for the slide.
2. 'contentPoints': An array of 2-4 key bullet points (strings).
3. 'imageSuggestion': (Optional) A brief description for a relevant image if applicable (e.g., "A brain with interconnected nodes for AI"). Set to null if no image is suitable.
4. 'diagramSuggestion': (Optional) An object with 'type' (e.g., "flowchart", "mindmap", "sequence") and 'description' (what the diagram should illustrate) if a diagram would be beneficial. Set to null if no diagram is suitable.

Return the result as a single JSON object with keys: 'title' (string, for the overall presentation) and 'slides' (an array of slide objects as described above).
Example for a slide: { "slideTitle": "Introduction to Topic", "contentPoints": ["Point 1", "Point 2"], "imageSuggestion": "Abstract representation of the topic", "diagramSuggestion": null }`;

  const response: GenerateContentResponse = await ai.models.generateContent({
    model: GEMINI_TEXT_MODEL,
    contents: prompt,
    config: {
      responseMimeType: "application/json",
      temperature: 0.5,
    },
  });
  return parseJsonFromGeminiResponse<Presentation>(response.text);
};

export const generateImageDescriptionForTopic = async (ai: GoogleGenAI, slideContext: string): Promise<string> => {
  const isArabic = isArabicText(slideContext);

  const prompt = isArabic ?
    `بناءً على سياق الشريحة: "${slideContext.substring(0,500)}"، قدم وصفاً بصرياً مختصراً (حد أقصى 15 كلمة) مناسب لنموذج إنشاء صور بالذكاء الاصطناعي لإنشاء صورة ذات صلة ومهنية. ركز على المفاهيم الأساسية أو الاستعارات. مثال: "شبكة عصبية متوهجة تربط نقاط بيانات متنوعة."` :
    `Based on the slide context: "${slideContext.substring(0,500)}", provide a concise, visually descriptive prompt (max 15 words) suitable for an AI image generation model to create a relevant, professional image. Focus on key concepts or metaphors. Example: "Glowing neural network connecting diverse data points."`;

  const response: GenerateContentResponse = await ai.models.generateContent({
    model: GEMINI_TEXT_MODEL,
    contents: prompt,
    config: { temperature: 0.7 }
  });
  return response.text.trim();
};

export const generateMermaidSyntaxForTopic = async (ai: GoogleGenAI, diagramType: string, diagramDescription: string): Promise<string> => {
  const isArabic = isArabicText(diagramDescription);

  const prompt = isArabic ?
    `قم بإنشاء صيغة MermaidJS لمخطط "${diagramType}" يوضح التالي: "${diagramDescription.substring(0,500)}".
أخرج فقط كتلة كود MermaidJS. لا تضمن أي نص أو شرح آخر.
استخدم النصوص العربية في المخطط عند الحاجة.

مثال على مخطط انسيابي:
graph TD;
    A[البداية] --> B(العملية الأولى);
    B --> C{قرار};
    C -->|نعم| D[النهاية];
    C -->|لا| E[العملية الثانية];
    E --> D;

مثال على خريطة ذهنية:
mindmap
  root((الموضوع))
    (الموضوع الفرعي الأول)
      (التفصيل أ)
      (التفصيل ب)
    (الموضوع الفرعي الثاني)

قدم صيغة MermaidJS كاملة وصحيحة.` :
    `Generate MermaidJS syntax for a "${diagramType}" diagram that illustrates the following: "${diagramDescription.substring(0,500)}".
Only output the MermaidJS code block. Do not include any other text or explanation.
For example, for a flowchart:
graph TD;
    A[Start] --> B(Process 1);
    B --> C{Decision};
    C -->|Yes| D[End];
    C -->|No| E[Process 2];
    E --> D;

For a mindmap:
mindmap
  root((Topic))
    (Sub-topic 1)
      (Detail A)
      (Detail B)
    (Sub-topic 2)

Provide the complete and valid MermaidJS syntax.`;

  const response: GenerateContentResponse = await ai.models.generateContent({
    model: GEMINI_TEXT_MODEL,
    contents: prompt,
    config: { temperature: 0.2 } // Lower temp for more accurate code generation
  });

  // Extract code if wrapped in markdown, otherwise use as is
  let mermaidCode = response.text.trim();
  const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
  const match = mermaidCode.match(fenceRegex);
  if (match && match[2]) {
    mermaidCode = match[2].trim();
  }
  return mermaidCode;
};
