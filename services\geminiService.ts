
import { GoogleGenAI, GenerateContentResponse } from '@google/genai';
import type { Topic, Presentation, Slide } from '../types';
import { GEMINI_TEXT_MODEL } from '../constants';
import { enhanceSlideWithRealContent, enhanceEntirePresentation } from './contentEnhancementService';
import { searchAllContent } from './webSearchService';

// Helper to parse JSON, handling potential markdown fences
const parseJsonFromGeminiResponse = <T,>(responseText: string): T => {
  let jsonStr = responseText.trim();
  const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
  const match = jsonStr.match(fenceRegex);
  if (match && match[2]) {
    jsonStr = match[2].trim();
  }
  try {
    return JSON.parse(jsonStr) as T;
  } catch (e) {
    console.error("Failed to parse JSON response:", jsonStr, e);
    throw new Error(`Invalid JSON response from AI: ${(e as Error).message}`);
  }
};


export const extractTopics = async (ai: GoogleGenAI, bookContent: string): Promise<Topic[]> => {
  const prompt = `Analyze the following book content (first 5000 characters) and extract up to 5 main topics. For each topic, provide a concise title and a brief summary (1-2 sentences). Return the result as a JSON array of objects, where each object has 'id' (a unique string, e.g., topic-1), 'title' (string), and 'summary' (string).
Content:
${bookContent.substring(0, 5000)}
`;

  const response: GenerateContentResponse = await ai.models.generateContent({
    model: GEMINI_TEXT_MODEL,
    contents: prompt,
    config: {
      responseMimeType: "application/json",
      temperature: 0.3, // Lower temperature for more deterministic topic extraction
    },
  });

  const topics = parseJsonFromGeminiResponse<Topic[]>(response.text);
  // Ensure IDs are unique if not provided by AI
  return topics.map((topic, index) => ({ ...topic, id: topic.id || `topic-${index + 1}` }));
};


export const generatePresentationOutline = async (ai: GoogleGenAI, topicTitle: string, topicContext: string): Promise<Presentation> => {
  const prompt = `
Generate a presentation outline for the topic "${topicTitle}" based on the following context:
Context: "${topicContext.substring(0, 3000)}"

The presentation should have a main title and 3-5 slides.
For each slide, provide:
1.  'slideTitle': A concise title for the slide.
2.  'contentPoints': An array of 2-4 key bullet points (strings).
3.  'imageSuggestion': (Optional) A brief description for a relevant image if applicable (e.g., "A brain with interconnected nodes for AI"). Set to null if no image is suitable.
4.  'diagramSuggestion': (Optional) An object with 'type' (e.g., "flowchart", "mindmap", "sequence") and 'description' (what the diagram should illustrate) if a diagram would be beneficial. Set to null if no diagram is suitable.

Return the result as a single JSON object with keys: 'title' (string, for the overall presentation) and 'slides' (an array of slide objects as described above).
Example for a slide: { "slideTitle": "Introduction to Topic", "contentPoints": ["Point 1", "Point 2"], "imageSuggestion": "Abstract representation of the topic", "diagramSuggestion": null }
`;

  const response: GenerateContentResponse = await ai.models.generateContent({
    model: GEMINI_TEXT_MODEL,
    contents: prompt,
    config: {
      responseMimeType: "application/json",
      temperature: 0.5,
    },
  });
  return parseJsonFromGeminiResponse<Presentation>(response.text);
};

// Enhanced presentation generation with real content
export const generateEnhancedPresentationOutline = async (
  ai: GoogleGenAI,
  topicTitle: string,
  topicContext: string
): Promise<Presentation> => {
  // First, generate the basic presentation outline
  const basicPresentation = await generatePresentationOutline(ai, topicTitle, topicContext);

  // Then enhance each slide with real content
  const enhancedSlides = await enhanceEntirePresentation(ai, basicPresentation.slides, topicContext);

  return {
    ...basicPresentation,
    slides: enhancedSlides
  };
};

// Generate presentation with web search integration
export const generatePresentationWithWebContent = async (
  ai: GoogleGenAI,
  topicTitle: string,
  topicContext: string
): Promise<Presentation> => {
  const isArabic = isArabicText(topicTitle + " " + topicContext);

  // Search for real content first
  const webContent = await searchAllContent(ai, topicTitle, '', topicContext);

  // Generate enhanced prompt with web content
  const enhancedPrompt = isArabic ?
    `قم بإنشاء عرض تقديمي شامل ومفصل للموضوع "${topicTitle}" باستخدام المعلومات التالية:

السياق الأساسي: "${topicContext.substring(0, 2000)}"

معلومات إضافية من البحث:
${webContent.information.map(info => `- ${info.title}: ${info.snippet}`).join('\n')}

إحصائيات حديثة:
${webContent.statistics.join('\n')}

يرجى إنشاء عرض تقديمي يتضمن:
1. عنوان رئيسي جذاب
2. 4-6 شرائح مفصلة
3. نقاط محتوى غنية بالمعلومات الحقيقية
4. اقتراحات صور محددة
5. مخططات بصرية مناسبة
6. عناصر تفاعلية (اختبارات أو فيديوهات)

تنسيق JSON المطلوب:
{
  "title": "عنوان العرض التقديمي",
  "slides": [
    {
      "slideTitle": "عنوان الشريحة",
      "contentPoints": ["نقطة مفصلة 1", "نقطة مفصلة 2", "نقطة مفصلة 3"],
      "imageSuggestion": "وصف دقيق للصورة المطلوبة",
      "diagramSuggestion": {
        "type": "نوع المخطط",
        "description": "وصف المخطط مع بيانات حقيقية"
      }
    }
  ]
}` :
    `Create a comprehensive and detailed presentation for the topic "${topicTitle}" using the following information:

Basic Context: "${topicContext.substring(0, 2000)}"

Additional Information from Research:
${webContent.information.map(info => `- ${info.title}: ${info.snippet}`).join('\n')}

Recent Statistics:
${webContent.statistics.join('\n')}

Please create a presentation that includes:
1. Engaging main title
2. 4-6 detailed slides
3. Content points rich with real information
4. Specific image suggestions
5. Appropriate visual diagrams
6. Interactive elements (quizzes or videos)

Required JSON format:
{
  "title": "Presentation Title",
  "slides": [
    {
      "slideTitle": "Slide Title",
      "contentPoints": ["Detailed point 1", "Detailed point 2", "Detailed point 3"],
      "imageSuggestion": "Specific description of required image",
      "diagramSuggestion": {
        "type": "diagram type",
        "description": "diagram description with real data"
      }
    }
  ]
}`;

  const response: GenerateContentResponse = await ai.models.generateContent({
    model: GEMINI_TEXT_MODEL,
    contents: enhancedPrompt,
    config: {
      responseMimeType: "application/json",
      temperature: 0.6,
    },
  });

  const presentation = parseJsonFromGeminiResponse<Presentation>(response.text);

  // Enhance slides with real images and interactive content
  const enhancedSlides = await Promise.all(
    presentation.slides.map(async (slide, index) => {
      // Add real images from web search
      if (webContent.images && webContent.images[index]) {
        slide.imageUrl = webContent.images[index].url;
        slide.searchTerms = webContent.images[index].searchTerms;
      }

      // Add interactive videos
      if (webContent.videos && webContent.videos[index] && index < 2) {
        slide.interaction = {
          type: 'video',
          videoUrl: webContent.videos[index].url,
          videoTitle: webContent.videos[index].title,
          description: webContent.videos[index].description
        };
      }

      // Add real-world examples and statistics
      slide.realWorldExamples = webContent.information.slice(0, 3).map(info => info.snippet);
      slide.statistics = webContent.statistics.slice(0, 2);
      slide.sources = webContent.information.slice(0, 2).map(info => info.source);

      return slide;
    })
  );

  return {
    ...presentation,
    slides: enhancedSlides
  };
};

export const generateImageDescriptionForTopic = async (ai: GoogleGenAI, slideContext: string): Promise<string> => {
  const prompt = `Based on the slide context: "${slideContext.substring(0,500)}", provide a concise, visually descriptive prompt (max 15 words) suitable for an AI image generation model to create a relevant, professional image. Focus on key concepts or metaphors. Example: "Glowing neural network connecting diverse data points."`;

  const response: GenerateContentResponse = await ai.models.generateContent({
    model: GEMINI_TEXT_MODEL,
    contents: prompt,
    config: { temperature: 0.7 }
  });
  return response.text.trim();
};

export const generateMermaidSyntaxForTopic = async (ai: GoogleGenAI, diagramType: string, diagramDescription: string): Promise<string> => {
  const prompt = `
Generate MermaidJS syntax for a "${diagramType}" diagram that illustrates the following: "${diagramDescription.substring(0,500)}".
Only output the MermaidJS code block. Do not include any other text or explanation.
For example, for a flowchart:
graph TD;
    A[Start] --> B(Process 1);
    B --> C{Decision};
    C -->|Yes| D[End];
    C -->|No| E[Process 2];
    E --> D;

For a mindmap:
mindmap
  root((Topic))
    (Sub-topic 1)
      (Detail A)
      (Detail B)
    (Sub-topic 2)

Provide the complete and valid MermaidJS syntax.
  `;
  const response: GenerateContentResponse = await ai.models.generateContent({
    model: GEMINI_TEXT_MODEL,
    contents: prompt,
    config: { temperature: 0.2 } // Lower temp for more accurate code generation
  });

  // Extract code if wrapped in markdown, otherwise use as is
  let mermaidCode = response.text.trim();
  const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
  const match = mermaidCode.match(fenceRegex);
  if (match && match[2]) {
    mermaidCode = match[2].trim();
  }
  return mermaidCode;
};
