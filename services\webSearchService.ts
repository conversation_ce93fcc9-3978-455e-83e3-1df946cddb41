// خدمة البحث على الويب للحصول على محتوى حقيقي - Web Search Service
// تدعم البحث عن المعلومات والصور والفيديوهات الحقيقية

import type { GoogleGenAI } from '@google/genai';

// Helper function to detect Arabic text
const isArabicText = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  const arabicChars = text.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g);
  const totalChars = text.replace(/\s/g, '').length;
  return arabicChars ? (arabicChars.length / totalChars) > 0.3 : false;
};

// Interface for search results
export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  source: string;
}

export interface ImageResult {
  url: string;
  title: string;
  source: string;
  searchTerms: string[];
}

export interface VideoResult {
  title: string;
  url: string;
  thumbnail: string;
  duration?: string;
  channel?: string;
  description?: string;
}

// Search for real information about a topic
export const searchTopicInformation = async (
  ai: GoogleGenAI,
  topic: string,
  context: string = ''
): Promise<SearchResult[]> => {
  const isArabic = isArabicText(topic + context);
  
  const prompt = isArabic ?
    `بناءً على الموضوع "${topic}" والسياق "${context.substring(0, 500)}"، قم بإنشاء قائمة بمصادر معلومات حقيقية وموثوقة.

يرجى تقديم 5-7 مصادر تتضمن:
1. عنوان المصدر
2. رابط مقترح (موقع موثوق)
3. ملخص قصير للمحتوى
4. نوع المصدر (أكاديمي، إخباري، تعليمي، إلخ)

أرجع النتيجة بتنسيق JSON:
[
  {
    "title": "عنوان المصدر",
    "url": "https://example.com",
    "snippet": "ملخص قصير للمحتوى",
    "source": "نوع المصدر"
  }
]` :
    `Based on the topic "${topic}" and context "${context.substring(0, 500)}", generate a list of real and reliable information sources.

Please provide 5-7 sources including:
1. Source title
2. Suggested URL (reliable website)
3. Short content summary
4. Source type (academic, news, educational, etc.)

Return the result in JSON format:
[
  {
    "title": "Source title",
    "url": "https://example.com",
    "snippet": "Short content summary",
    "source": "Source type"
  }
]`;

  try {
    const response = await ai.models.generateContent({
      model: 'gemini-1.5-flash',
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        temperature: 0.4
      }
    });

    return JSON.parse(response.text);
  } catch (error) {
    console.error('Error searching topic information:', error);
    return [];
  }
};

// Search for relevant images
export const searchTopicImages = async (
  ai: GoogleGenAI,
  topic: string,
  slideTitle: string = ''
): Promise<ImageResult[]> => {
  const isArabic = isArabicText(topic + slideTitle);
  
  const prompt = isArabic ?
    `بناءً على الموضوع "${topic}" وعنوان الشريحة "${slideTitle}"، اقترح صور حقيقية ومناسبة.

يرجى تقديم 3-5 اقتراحات صور تتضمن:
1. رابط الصورة من مصادر مفتوحة (Unsplash, Pixabay, Pexels)
2. عنوان وصفي للصورة
3. مصدر الصورة
4. مصطلحات البحث المستخدمة

أرجع النتيجة بتنسيق JSON:
[
  {
    "url": "https://source.unsplash.com/800x600/?term",
    "title": "عنوان وصفي للصورة",
    "source": "Unsplash",
    "searchTerms": ["مصطلح 1", "مصطلح 2"]
  }
]` :
    `Based on the topic "${topic}" and slide title "${slideTitle}", suggest real and appropriate images.

Please provide 3-5 image suggestions including:
1. Image URL from open sources (Unsplash, Pixabay, Pexels)
2. Descriptive image title
3. Image source
4. Search terms used

Return the result in JSON format:
[
  {
    "url": "https://source.unsplash.com/800x600/?term",
    "title": "Descriptive image title",
    "source": "Unsplash",
    "searchTerms": ["term 1", "term 2"]
  }
]`;

  try {
    const response = await ai.models.generateContent({
      model: 'gemini-1.5-flash',
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        temperature: 0.6
      }
    });

    const results = JSON.parse(response.text);
    
    // Generate actual Unsplash URLs
    return results.map((result: any) => ({
      ...result,
      url: generateUnsplashUrl(result.searchTerms || [topic])
    }));
  } catch (error) {
    console.error('Error searching topic images:', error);
    return [{
      url: generateUnsplashUrl([topic]),
      title: `Image related to ${topic}`,
      source: 'Unsplash',
      searchTerms: [topic]
    }];
  }
};

// Search for educational videos
export const searchEducationalVideos = async (
  ai: GoogleGenAI,
  topic: string,
  language: 'ar' | 'en' = 'en'
): Promise<VideoResult[]> => {
  const isArabic = language === 'ar' || isArabicText(topic);
  
  const prompt = isArabic ?
    `بناءً على الموضوع "${topic}"، اقترح فيديوهات تعليمية حقيقية ومفيدة.

يرجى تقديم 3-5 فيديوهات تتضمن:
1. عنوان الفيديو
2. رابط البحث في YouTube
3. صورة مصغرة مقترحة
4. مدة الفيديو المتوقعة
5. اسم القناة المقترحة
6. وصف قصير

أرجع النتيجة بتنسيق JSON:
[
  {
    "title": "عنوان الفيديو",
    "url": "https://www.youtube.com/results?search_query=...",
    "thumbnail": "رابط الصورة المصغرة",
    "duration": "10:30",
    "channel": "اسم القناة",
    "description": "وصف قصير للفيديو"
  }
]` :
    `Based on the topic "${topic}", suggest real and useful educational videos.

Please provide 3-5 videos including:
1. Video title
2. YouTube search URL
3. Suggested thumbnail
4. Expected video duration
5. Suggested channel name
6. Short description

Return the result in JSON format:
[
  {
    "title": "Video title",
    "url": "https://www.youtube.com/results?search_query=...",
    "thumbnail": "thumbnail URL",
    "duration": "10:30",
    "channel": "Channel name",
    "description": "Short video description"
  }
]`;

  try {
    const response = await ai.models.generateContent({
      model: 'gemini-1.5-flash',
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        temperature: 0.6
      }
    });

    const results = JSON.parse(response.text);
    
    // Generate actual YouTube search URLs
    return results.map((result: any) => ({
      ...result,
      url: generateYouTubeSearchUrl(result.title),
      thumbnail: generateVideoThumbnail(result.title)
    }));
  } catch (error) {
    console.error('Error searching educational videos:', error);
    return [{
      title: `Educational video about ${topic}`,
      url: generateYouTubeSearchUrl(topic),
      thumbnail: generateVideoThumbnail(topic),
      duration: '10:00',
      channel: 'Educational Channel',
      description: `Learn about ${topic}`
    }];
  }
};

// Get current statistics and data
export const getCurrentStatistics = async (
  ai: GoogleGenAI,
  topic: string
): Promise<string[]> => {
  const isArabic = isArabicText(topic);
  
  const prompt = isArabic ?
    `قدم إحصائيات وبيانات حديثة ومحدثة حول الموضوع: "${topic}"

يرجى تقديم 3-5 إحصائيات أو حقائق رقمية حديثة مع:
1. الرقم أو النسبة
2. مصدر البيانات
3. سنة البيانات

مثال: "70% من الشركات تستخدم الذكاء الاصطناعي (McKinsey 2024)"

أرجع النتيجة كمصفوفة من النصوص:
["إحصائية 1", "إحصائية 2", "إحصائية 3"]` :
    `Provide current and updated statistics about the topic: "${topic}"

Please provide 3-5 recent statistics or numerical facts with:
1. The number or percentage
2. Data source
3. Year of data

Example: "70% of companies use AI (McKinsey 2024)"

Return the result as an array of strings:
["Statistic 1", "Statistic 2", "Statistic 3"]`;

  try {
    const response = await ai.models.generateContent({
      model: 'gemini-1.5-flash',
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        temperature: 0.3
      }
    });

    return JSON.parse(response.text);
  } catch (error) {
    console.error('Error getting current statistics:', error);
    return [];
  }
};

// Helper functions
const generateUnsplashUrl = (searchTerms: string[]): string => {
  const query = searchTerms.join(',');
  return `https://source.unsplash.com/800x600/?${encodeURIComponent(query)}`;
};

const generateYouTubeSearchUrl = (query: string): string => {
  return `https://www.youtube.com/results?search_query=${encodeURIComponent(query)}`;
};

const generateVideoThumbnail = (title: string): string => {
  // Generate a placeholder thumbnail based on the title
  const encodedTitle = encodeURIComponent(title.substring(0, 50));
  return `https://source.unsplash.com/400x300/?video,${encodedTitle}`;
};

// Comprehensive search for all content types
export const searchAllContent = async (
  ai: GoogleGenAI,
  topic: string,
  slideTitle: string = '',
  context: string = ''
) => {
  const [information, images, videos, statistics] = await Promise.all([
    searchTopicInformation(ai, topic, context),
    searchTopicImages(ai, topic, slideTitle),
    searchEducationalVideos(ai, topic),
    getCurrentStatistics(ai, topic)
  ]);

  return {
    information,
    images,
    videos,
    statistics
  };
};
