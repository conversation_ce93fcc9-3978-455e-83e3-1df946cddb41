/* Enhanced CSS Styles for AI Interactive Book Chat with PDF Flash Explorer - Version 1.2.1 */

/* Root CSS Variables */
:root {
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  --accent-50: #f0f9ff;
  --accent-100: #e0f2fe;
  --accent-200: #bae6fd;
  --accent-300: #7dd3fc;
  --accent-400: #38bdf8;
  --accent-500: #0ea5e9;
  --accent-600: #0284c7;
  --accent-700: #0369a1;
  --accent-800: #075985;
  --accent-900: #0c4a6e;

  --background-primary: #0f172a;
  --background-secondary: #1e293b;
  --background-tertiary: #334155;
  --background-quaternary: #475569;

  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --text-quaternary: #64748b;

  --border-radius-sm: 6px;
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 20px;

  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.6s ease;

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  --gradient-primary: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 50%, var(--accent-900) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-500) 0%, var(--accent-600) 50%, var(--accent-700) 100%);
  --gradient-text: linear-gradient(135deg, var(--accent-400) 0%, var(--accent-300) 100%);
}

/* Global Styles with Enhanced RTL Support */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  line-height: 1.6;
  height: 100%;
}

body {
  font-family: 'Noto Sans Arabic', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
  direction: rtl;
  background: var(--gradient-primary);
  min-height: 100vh;
  color: var(--text-primary);
}

/* Enhanced Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.loading-screen.fade-out {
  opacity: 0;
  pointer-events: none;
}

.loading-content {
  text-align: center;
  will-change: transform;
  animation: float 3s ease-in-out infinite;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
  border: 4px solid var(--accent-200);
  border-top: 4px solid var(--accent-500);
  border-radius: 50%;
  will-change: transform;
  animation: spin 1s linear infinite;
}

/* Performance-optimized animations using GPU acceleration */
@keyframes spin {
  0% {
    transform: rotate3d(0, 0, 1, 0deg);
    will-change: transform;
  }
  100% {
    transform: rotate3d(0, 0, 1, 360deg);
    will-change: transform;
  }
}

@keyframes float {
  0%, 100% {
    transform: translate3d(0, 0px, 0);
    will-change: transform;
  }
  50% {
    transform: translate3d(0, -20px, 0);
    will-change: transform;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
    will-change: opacity, transform;
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
    will-change: opacity, transform;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
    will-change: opacity, transform;
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
    will-change: opacity, transform;
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translate3d(30px, 0, 0);
    will-change: opacity, transform;
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
    will-change: opacity, transform;
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translate3d(-30px, 0, 0);
    will-change: opacity, transform;
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
    will-change: opacity, transform;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale3d(0.9, 0.9, 1);
    will-change: opacity, transform;
  }
  to {
    opacity: 1;
    transform: scale3d(1, 1, 1);
    will-change: opacity, transform;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    will-change: opacity;
  }
  50% {
    opacity: 0.7;
    will-change: opacity;
  }
}

/* Animation delay utilities */
.bounce-delay-0 { animation-delay: 0ms; }
.bounce-delay-150 { animation-delay: 150ms; }
.bounce-delay-300 { animation-delay: 300ms; }

/* Page animations */
.animate-fade-in {
  animation: fadeIn 0.8s ease-in-out;
  will-change: opacity, transform;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
  will-change: opacity, transform;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
  will-change: opacity, transform;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
  will-change: opacity, transform;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
  will-change: opacity, transform;
}

/* Enhanced Mermaid Diagram Styles */
.mermaid {
  background: transparent;
  border-radius: var(--border-radius);
  padding: 16px;
  margin: 16px 0;
  direction: ltr; /* Keep diagrams LTR */
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.mermaid svg {
  max-width: 100%;
  height: auto;
  background: transparent;
  border-radius: var(--border-radius-sm);
}

/* Enhanced Page Transition Styles with RTL Support */
.page-content {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: transform var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1),
              opacity var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: var(--border-radius);
  overflow: hidden;
  will-change: transform, opacity;
  box-shadow: var(--shadow-xl);
}

.page-enter {
  transform: translate3d(0, 0, 0);
  opacity: 1;
}

/* RTL-aware page transitions */
.page-exit-left {
  transform: translate3d(-20%, 0, 0);
  opacity: 0;
}

.page-enter-from-right {
  transform: translate3d(20%, 0, 0);
  opacity: 0;
}

.page-exit-right {
  transform: translate3d(20%, 0, 0);
  opacity: 0;
}

.page-enter-from-left {
  transform: translate3d(-20%, 0, 0);
  opacity: 0;
}

/* Enhanced Card Styles */
.card-enhanced {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-xl);
  transition: all var(--transition-normal);
  will-change: transform, box-shadow;
}

.card-enhanced:hover {
  transform: translate3d(0, -4px, 0);
  box-shadow: var(--shadow-2xl);
  border-color: rgba(148, 163, 184, 0.2);
}

/* Utility classes */
.perspective-3d {
  perspective: 1500px;
}

.break-words {
  word-wrap: break-word;
  white-space: pre-wrap;
  overflow-wrap: break-word;
}

.glass-effect {
  background: rgba(30, 41, 59, 0.7);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.gradient-text {
  background: var(--gradient-text);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.gradient-border {
  position: relative;
  background: var(--gradient-accent);
  border-radius: var(--border-radius);
  padding: 2px;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 2px;
  background: var(--background-secondary);
  border-radius: calc(var(--border-radius) - 2px);
}

/* Enhanced Button Styles */
.btn-primary {
  background: var(--gradient-accent);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  padding: 12px 24px;
  font-weight: 600;
  transition: all var(--transition-normal);
  cursor: pointer;
  box-shadow: var(--shadow-md);
  will-change: transform, box-shadow;
}

.btn-primary:hover {
  transform: translate3d(0, -2px, 0);
  box-shadow: var(--shadow-lg);
}

.btn-primary:active {
  transform: translate3d(0, 0, 0);
  box-shadow: var(--shadow);
}

.btn-secondary {
  background: rgba(51, 65, 85, 0.8);
  color: var(--text-primary);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: var(--border-radius-sm);
  padding: 12px 24px;
  font-weight: 500;
  transition: all var(--transition-normal);
  cursor: pointer;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  will-change: transform, background-color;
}

.btn-secondary:hover {
  background: rgba(51, 65, 85, 1);
  border-color: rgba(148, 163, 184, 0.3);
  transform: translate3d(0, -1px, 0);
}

/* Loading and error states */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--text-tertiary);
  flex-direction: column;
  gap: 16px;
}

.loading-state .spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--accent-200);
  border-top: 3px solid var(--accent-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-state {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: var(--border-radius);
  padding: 16px;
  color: #fca5a5;
  text-align: center;
}

.success-state {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: var(--border-radius);
  padding: 16px;
  color: #86efac;
  text-align: center;
}

/* Enhanced scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--accent-600);
  border-radius: 4px;
  transition: background var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-500);
}

/* Selection styles */
::selection {
  background: var(--accent-500);
  color: white;
}

::-moz-selection {
  background: var(--accent-500);
  color: white;
}

/* TTS (Text-to-Speech) Enhanced Styles */
.tts-reader {
  background: rgba(30, 41, 59, 0.9);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: var(--border-radius);
  border: 1px solid rgba(148, 163, 184, 0.1);
  padding: 20px;
  box-shadow: var(--shadow-xl);
}

.tts-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.tts-voice-select {
  background: rgba(51, 65, 85, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  padding: 10px 14px;
  transition: all var(--transition-normal);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  min-width: 200px;
}

.tts-voice-select:focus {
  outline: none;
  border-color: var(--accent-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.tts-slider {
  -webkit-appearance: none;
  appearance: none;
  height: 8px;
  background: rgba(51, 65, 85, 0.8);
  border-radius: 4px;
  outline: none;
  transition: all var(--transition-normal);
  cursor: pointer;
  min-width: 150px;
}

.tts-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--accent-500);
  border-radius: 50%;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  will-change: transform, background-color;
}

.tts-slider::-webkit-slider-thumb:hover {
  background: var(--accent-400);
  transform: scale3d(1.1, 1.1, 1);
  box-shadow: var(--shadow-lg);
}

.tts-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: var(--accent-500);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.tts-slider::-moz-range-thumb:hover {
  background: var(--accent-400);
  transform: scale3d(1.1, 1.1, 1);
}

.tts-text-highlight {
  background: linear-gradient(120deg, rgba(255, 235, 59, 0.3) 0%, rgba(255, 193, 7, 0.3) 100%);
  color: var(--text-primary);
  padding: 3px 6px;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-normal);
  animation: pulse 1.5s ease-in-out infinite;
  box-shadow: 0 0 10px rgba(255, 235, 59, 0.2);
}

.tts-language-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(14, 165, 233, 0.1);
  border: 1px solid rgba(14, 165, 233, 0.3);
  border-radius: var(--border-radius-sm);
  font-size: 13px;
  color: var(--accent-400);
  font-weight: 500;
}

.tts-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.tts-panel {
  background: var(--background-secondary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-2xl);
  border: 1px solid rgba(148, 163, 184, 0.1);
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

/* PDF Flash Explorer Enhanced Styles */
.pdf-flash-container {
  position: relative;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.pdf-page-canvas {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-lg);
  transition: transform var(--transition-normal);
}

.pdf-page-canvas:hover {
  transform: scale3d(1.02, 1.02, 1);
}

.pdf-controls {
  background: rgba(30, 41, 59, 0.9);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  padding: 16px;
}

.pdf-thumbnail {
  width: 70px;
  height: 90px;
  border-radius: var(--border-radius-sm);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all var(--transition-normal);
  overflow: hidden;
  position: relative;
  box-shadow: var(--shadow-md);
  will-change: transform, border-color;
}

.pdf-thumbnail:hover {
  border-color: var(--accent-500);
  transform: scale3d(1.05, 1.05, 1);
  box-shadow: var(--shadow-lg);
}

.pdf-thumbnail.active {
  border-color: var(--accent-600);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.3);
  transform: scale3d(1.05, 1.05, 1);
}

.pdf-search-highlight {
  background: rgba(255, 235, 59, 0.4);
  border-radius: 2px;
  padding: 1px 2px;
  box-shadow: 0 0 5px rgba(255, 235, 59, 0.3);
}

.pdf-zoom-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(51, 65, 85, 0.8);
  border-radius: var(--border-radius-sm);
  padding: 8px 12px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.pdf-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  z-index: 9998;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Enhanced Form Styles */
.form-input {
  background: rgba(51, 65, 85, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  padding: 12px 16px;
  transition: all var(--transition-normal);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  width: 100%;
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
  background: rgba(51, 65, 85, 1);
}

.form-input::placeholder {
  color: var(--text-quaternary);
}

.form-label {
  display: block;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 6px;
  font-size: 14px;
}

/* Enhanced Navigation */
.nav-item {
  padding: 12px 20px;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-normal);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--text-secondary);
  text-decoration: none;
}

.nav-item:hover {
  background: rgba(51, 65, 85, 0.5);
  color: var(--text-primary);
  transform: translate3d(4px, 0, 0);
}

.nav-item.active {
  background: var(--gradient-accent);
  color: white;
  box-shadow: var(--shadow-md);
}

/* Enhanced Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  background: var(--background-secondary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-2xl);
  border: 1px solid rgba(148, 163, 184, 0.1);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

/* Enhanced Tooltip */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  z-index: 1000;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.tooltip:hover::after {
  opacity: 1;
  visibility: visible;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .tts-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .tts-slider {
    min-width: 100%;
  }

  .pdf-thumbnail {
    width: 60px;
    height: 80px;
  }
}

@media (max-width: 768px) {
  :root {
    --border-radius: 8px;
    --border-radius-lg: 12px;
  }

  body {
    font-size: 14px;
  }

  .tts-panel {
    margin: 10px;
    max-height: 95vh;
  }

  .modal-content {
    margin: 10px;
    max-height: 90vh;
  }

  .pdf-controls {
    padding: 12px;
  }

  .loading-spinner {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .tts-voice-select {
    min-width: 100%;
  }

  .pdf-zoom-controls {
    flex-wrap: wrap;
    gap: 8px;
  }

  .nav-item {
    padding: 10px 16px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .tts-text-highlight,
  .loading-content,
  .loading-spinner,
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --background-primary: #000000;
    --background-secondary: #1a1a1a;
    --text-primary: #ffffff;
    --accent-500: #00bfff;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  :root {
    --background-primary: #0a0a0a;
    --background-secondary: #1a1a1a;
    --background-tertiary: #2a2a2a;
  }
}
