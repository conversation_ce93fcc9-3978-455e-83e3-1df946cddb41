import path from 'path';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      define: {
        'process.env.API_KEY': JSON.stringify(env.API_KEY || env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.API_KEY || env.GEMINI_API_KEY)
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      },
      server: {
        host: true,
        port: 5173,
        strictPort: false,
        open: false,
        cors: true,
        hmr: {
          overlay: true
        }
      },
      preview: {
        host: true,
        port: 4173,
        strictPort: false,
        open: false,
        cors: true
      },
      build: {
        target: 'esnext',
        minify: 'esbuild',
        sourcemap: false,
        rollupOptions: {
          output: {
            manualChunks: {
              vendor: ['react', 'react-dom'],
              gemini: ['@google/genai']
            }
          }
        }
      },
      optimizeDeps: {
        include: ['react', 'react-dom', '@google/genai'],
        exclude: []
      },
      esbuild: {
        target: 'esnext',
        format: 'esm'
      }
    };
});
